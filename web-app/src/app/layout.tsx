import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";

// Import search components
import { SearchProvider } from "@/components/search/search-provider";
import { SearchDialog } from "@/components/search/search-dialog";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Instabee API Documentation",
  description: "Documentation for the Instabee delivery platform API",
  keywords: ["API", "documentation", "delivery", "Instabee", "parcel", "shipping"],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <SearchProvider>
          {children}
          <SearchDialog />
        </SearchProvider>
      </body>
    </html>
  );
}
