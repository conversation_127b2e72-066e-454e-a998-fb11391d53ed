# Create Parcel API (Post Packing)

The Post Packing API is used to update a parcel's status after it has been physically packed and is ready for pickup and delivery. This is the final confirmation step after the initial Post Purchase prebooking.

## Overview

**Post Purchase vs Post Packing**: The Post Purchase flow is like a prebooking that reserves delivery capacity. The Post Packing flow is the final confirmation after physical packing, providing actual dimensions and confirming the parcel is ready for pickup.

This endpoint should be called after:
1. A parcel has been registered with the Post Purchase API
2. The order has been physically packed and prepared for shipping
3. The final dimensions and weight are known

Merchants can send information in different steps - some may provide all details in Post Purchase, others may provide minimal information initially and complete details in Post Packing.

## API Endpoint

| Endpoint | Method | Content-Type |
|----------|--------|-------------|
| `api.instabee.com/orders` | PUT | application/json |

## Integration Levels

### Bare Minimum
If everything was sent in the Post Purchase call, you only need:

```json
{
  "availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190",
  "parcelPackingConfirmed": true
}
```

### Minimum Required
Basic information needed for packing confirmation:

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|--------|
| **parcelPackingConfirmed** | boolean | required | Set to true to confirm parcel is packed | Must be true | Indicates packing is complete |
| **parcelId** | string | optional | Your reference ID for the parcel | Can be auto-generated if omitted | |
| **brand** | string | required | Brand for the delivery | "instabox" or "budbee" | |
| **product** | string | required | Product type for the delivery | "LOCKER_EXPRESS", "HOME_DELIVERY", etc. | |

### Core Parameters

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|---------|
| **availabilityToken** | string | optional | Token from the availability check | OneOf this, checkoutId, or orderNumber | Links to availability check |
| **parcelId** | string | optional | Your reference ID for the parcel | Can be auto-generated if omitted | Used for your own tracking |
| **parcelGroupId** | string | optional | Groups multiple parcels together | | For multi-parcel orders |
| **brandId** | string | optional | Your brand identifier | | For multi-brand merchants |
| **brand** | string | required | Brand for the delivery | "instabox" or "budbee" | Determines delivery network |
| **product** | string | required | Product type for the delivery | "LOCKER_EXPRESS", "HOME_DELIVERY", etc. | Defines service level |
| **parcelPackingConfirmed** | boolean | required | Set to true to confirm parcel is packed | Must be true | Indicates packing is complete |

### Recipient Information

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|---------|
| **recipient.name** | string | required | Recipient name | | |
| **recipient.email** | string | required | Recipient email address | Valid email format | Used for delivery notifications |
| **recipient.phone** | string | required | Recipient phone number | Min: 6 digits, Max 15 digits | Can be made optional with setting |
| **recipient.ssn** | string | optional | Social security number | | For age verification services |
| **recipient.street** | string | required | Recipient street address | | |
| **recipient.street2** | string | optional | Additional address information | | Floor, apartment, etc. |
| **recipient.postalCode** | string | required | Recipient postal code | | |
| **recipient.city** | string | required | Recipient city | | |
| **recipient.countryCode** | string | required | Recipient country code | ISO 3166 alpha 2 | e.g., "SE", "DK", "NO" |
| **recipient.coordinates** | object | optional | GPS coordinates | | For precise location |
| **recipient.coordinates.lat** | number | optional | Latitude | | |
| **recipient.coordinates.long** | number | optional | Longitude | | |

### Sender Information

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|---------|
| **sender.name** | string | optional | Sender/merchant name | | |
| **sender.email** | string | optional | Sender email address | Valid email format | |
| **sender.phone** | string | optional | Sender phone number | | |
| **sender.street** | string | optional | Sender street address | | |
| **sender.street2** | string | optional | Additional sender address info | | |
| **sender.postalCode** | string | optional | Sender postal code | | |
| **sender.city** | string | optional | Sender city | | |
| **sender.countryCode** | string | optional | Sender country code | ISO 3166 alpha 2 | |
| **sender.coordinates** | object | optional | Sender GPS coordinates | | |
| **sender.coordinates.lat** | number | optional | Latitude | | |
| **sender.coordinates.long** | number | optional | Longitude | | |

### Delivery Options

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|---------|
| **deliveryOption.sort_code** | string | required for locker | Sort code for the locker | | Identifies specific locker |
| **deliveryOption.slotId** | string | required for home delivery | ID of the selected delivery slot | | Obtained from availability check |
| **deliveryOption.etaInterval** | object | optional | Preferred delivery time window | | |
| **deliveryOption.etaInterval.from** | string | optional | Start of delivery window | ISO-8601 datetime | |
| **deliveryOption.etaInterval.to** | string | optional | End of delivery window | ISO-8601 datetime | Will default to best available if doesn't match |

### Dispatch Information

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|---------|
| **dispatch.readyToShip** | string | optional | When parcel is ready for pickup | ISO-8601 datetime | Only one of readyToShip/readyToPack/outOfStock |
| **dispatch.readyToPack** | string | optional | When packing process will start | ISO-8601 datetime | Instabee adds packing time to this |
| **dispatch.outOfStock** | boolean | optional | Product is out of stock | true/false | Removes ETA from delivery options |
| **dispatch.doNotDeliverBefore** | string | optional | Earliest delivery date allowed | ISO-8601 datetime | ETA will be on or after this date |
| **dispatch.packingTime** | number | optional | Required packing time in minutes | | |
| **dispatch.collectionPointId** | string | optional | Specific warehouse/pickup point | | Configured by Instabee |
| **dispatch.PackStation** | string | optional | Specific packing station location | | e.g., "Floor 1 Station 3" |
| **dispatch.returnPointId** | string | optional | Return address point ID | | For failed deliveries |

### Options and Settings

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|---------|
| **options.languageCode** | string | optional | Language for customer communications | ISO 639-1 | e.g., "EN", "SE", "NO" |

### Delivery Instructions

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|---------|
| **deliveryInstructions.notifyBy** | string | optional | How to notify recipient | "RING_DOORBELL", "KNOCK_ON_DOOR" | Uppercase in Post Packing |
| **deliveryInstructions.doorCode** | string | optional | Door or building access code | | |
| **deliveryInstructions.message** | string | optional | Special delivery instructions | | Free text message |
| **deliveryInstructions.intercom** | boolean | optional | Use intercom if available | true/false | |

### Additional Services

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|---------|
| **additionalServices.identification** | object | optional | Identity verification requirements | | |
| **additionalServices.identification.type** | string | optional | Type of verification | "AGE_LIMIT", "AGE_LIMIT_AT_HANDOVER", "SPECIFIC_PERSON", "ANY_PERSON" | Uppercase in Post Packing |
| **additionalServices.identification.ageLimit** | number | optional | Minimum age required | | Used with age_limit types |
| **additionalServices.identification.ssn** | string | optional | Required SSN for verification | | |
| **additionalServices.identification.name** | string | optional | Required name for verification | | |
| **additionalServices.leaveByDoor** | string | optional | Allow leaving parcel by door | "ALLOW", "DISALLOW", "FORCE" | Uppercase in Post Packing |
| **additionalServices.leaveWithNeighbour** | string | optional | Allow leaving with neighbor | "ALLOW", "DISALLOW", "FORCE" | Uppercase in Post Packing |
| **additionalServices.numberOfMissRetries** | number | optional | Number of delivery retry attempts | Minimum: 1 | null = default |

### Cart Information

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|---------|
| **cart.checkoutId** | string | optional | Checkout session ID | OneOf this, orderNumber, or availabilityToken | |
| **cart.orderNumber** | string | required* | Your order number | OneOf this, checkoutId, or availabilityToken | Used to link with your system |
| **cart.totalValueInCents** | number | optional | Total order value in cents | | Used for insurance purposes |
| **cart.totalWeightGram** | number | optional | Total weight in grams | | For logistics planning |
| **cart.parcel** | object | optional | Parcel dimensions and contents | | Detailed parcel information |

### Parcel Details

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|---------|
| **cart.parcel.heightCm** | number | optional | Parcel height in cm | | |
| **cart.parcel.widthCm** | number | optional | Parcel width in cm | | |
| **cart.parcel.lengthCm** | number | optional | Parcel length in cm | | |
| **cart.parcel.volumeDm3** | number | optional | Parcel volume in dm³ | | |
| **cart.parcel.estimatedSize** | string | optional | Estimated size category | "SMALL", "MEDIUM", "LARGE" | Uppercase in Post Packing |
| **cart.parcel.weightGram** | number | optional | Parcel weight in grams | | |
| **cart.parcel.type** | string | optional | Parcel type | "BOX", "ENVELOPE", "BAG" | Uppercase in Post Packing |
| **cart.parcel.products** | array | optional | Array of products in parcel | | Detailed product information |

### Product Details

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|---------|
| **cart.parcel.products[].name** | string | optional | Product name | | |
| **cart.parcel.products[].quantity** | number | optional | Quantity of this product | | |
| **cart.parcel.products[].productId** | string | optional | Your product identifier | | |
| **cart.parcel.products[].details** | object | optional | Detailed product information | | |
| **cart.parcel.products[].details.productType** | string | optional | Type of product | e.g., "Prescription" | |
| **cart.parcel.products[].details.imgUrl** | string | optional | Product image URL | | |
| **cart.parcel.products[].details.category** | string | optional | Product category | | |
| **cart.parcel.products[].details.brand** | string | optional | Product brand | | |
| **cart.parcel.products[].details.description** | string | optional | Product description | | |
| **cart.parcel.products[].details.price** | object | optional | Price information | | |
| **cart.parcel.products[].details.price.priceInCents** | number | optional | Price in cents | | |
| **cart.parcel.products[].details.price.taxRateInCents** | number | optional | Tax amount in cents | | |
| **cart.parcel.products[].details.price.discountRateInCents** | number | optional | Discount amount in cents | | |
| **cart.parcel.products[].details.price.currency** | string | optional | Currency code | ISO 4217 | e.g., "SEK" |
| **cart.parcel.products[].details.temperature** | object | optional | Temperature requirements | | |
| **cart.parcel.products[].details.temperature.min** | number | optional | Minimum temperature in Celsius | | |
| **cart.parcel.products[].details.temperature.max** | number | optional | Maximum temperature in Celsius | | |

### Package and Barcode Information

| Parameter | Type | Required | Description | Constraints | Comment |
|-----------|------|----------|-------------|------------|---------|
| **cart.parcel.products[].packages** | array | optional | Physical package information | | |
| **cart.parcel.products[].packages[].widthCm** | number | optional | Package width in cm | | Note: Different from Post Purchase (widthMm) |
| **cart.parcel.products[].packages[].heightCm** | number | optional | Package height in cm | | Note: Different from Post Purchase (heightMm) |
| **cart.parcel.products[].packages[].lengthCm** | number | optional | Package length in cm | | Note: Different from Post Purchase (lengthMm) |
| **cart.parcel.products[].packages[].weightCm** | number | optional | Package weight | | Note: Different unit from Post Purchase |
| **cart.parcel.products[].packages[].volumeDm3** | number | optional | Package volume in dm³ | | Note: Different from Post Purchase (volumeMm3) |
| **cart.parcel.products[].packages[].barcodes** | array | optional | Barcode information | | |
| **cart.parcel.products[].packages[].barcodes[].code** | string | optional | Barcode value | | |
| **cart.parcel.products[].packages[].barcodes[].type** | string | optional | Barcode type | "EAN13", etc. | Uppercase in Post Packing |

## Example Request - Bare Minimum

```json
{
  "availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190",
  "parcelPackingConfirmed": true
}
```

## Example Request - Minimum

```json
{
  "parcelId": "PREFIX1234567890",
  "brand": "instabox",
  "product": "LOCKER_EXPRESS",
  "parcelPackingConfirmed": true,
  "recipient": {
    "name": "Test Testsson",
    "email": "<EMAIL>",
    "phone": "0701234567",
    "street": "Hälsingegatan 40",
    "street2": "Floor 10",
    "postalCode": "12345",
    "city": "Stockholm",
    "countryCode": "SE"
  },
  "deliveryOption": {
    "sort_code": "IN123"
  },
  "cart": {
    "orderNumber": "12345"
  }
}
```

## Example Request - Maximum (Complete Order)

```json
{
  "parcelPackingConfirmed": true,
  "brand": "instabox",
  "product": "LOCKER_EXPRESS",
  "parcelGroupId": "123456789",
  "availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190",
  "parcelId": "PREFIX1234567890",
  "brandId": "Brand1",
  "sender": {
    "name": "Merchant A",
    "email": "<EMAIL>",
    "phone": "0701234567",
    "street": "Hälsingegatan 40",
    "street2": "Floor 10",
    "postalCode": "12345",
    "city": "Stockholm",
    "countryCode": "SE",
    "coordinates": {
      "long": 59.1111,
      "lat": 15.1111
    }
  },
  "recipient": {
    "name": "Test Testsson",
    "ssn": "199004152012",
    "email": "<EMAIL>",
    "phone": "0701234567",
    "street": "Hälsingegatan 40",
    "street2": "Floor 10",
    "postalCode": "12345",
    "city": "Stockholm",
    "countryCode": "SE",
    "coordinates": {
      "long": 59.1111,
      "lat": 15.1111
    }
  },
  "deliveryOption": {
    "sort_code": "IN123"
  },
  "dispatch": {
    "readyToShip": "2025-12-01T01:23:45.678Z",
    "collectionPointId": "STOCKHOLM_12345",
    "PackStation": "Floor 1 Station 3",
    "returnPointId": "GBG_123"
  },
  "options": {
    "languageCode": "EN"
  },
  "deliveryInstructions": {
    "notifyBy": "RING_DOORBELL",
    "doorCode": "1234",
    "message": "Hide it under the rock in the back",
    "intercom": true
  },
  "additionalServices": {
    "identification": {
      "type": "AGE_LIMIT",
      "ageLimit": 18,
      "ssn": "199004152012",
      "name": "John Doe"
    },
    "leaveByDoor": "ALLOW",
    "leaveWithNeighbour": "ALLOW",
    "numberOfMissRetries": 9
  },
  "cart": {
    "checkoutId": "XBO12-2923048",
    "orderNumber": "12345",
    "totalValueInCents": 12300,
    "totalWeightGram": 30000,
    "parcel": {
      "heightCm": 10,
      "widthCm": 10,
      "lengthCm": 10,
      "volumeDm3": 1,
      "estimatedSize": "SMALL",
      "weightGram": 2000,
      "type": "BOX",
      "products": [
        {
          "name": "My Little Pony Deathmetal Limited Edition",
          "quantity": 1,
          "productId": "1234567",
          "details": {
            "productType": "Prescription",
            "imgUrl": "https://tinyurl.com/2p9bu4kz",
            "category": "Toy Horses",
            "brand": "My Little Pony",
            "description": "Let the best worlds of Death Metal and Ponies be combined with this awesome toy",
            "price": {
              "priceInCents": 14900,
              "taxRateInCents": 2500,
              "discountRateInCents": 10,
              "currency": "SEK"
            },
            "temperature": {
              "min": 8,
              "max": 25
            }
          },
          "packages": [
            {
              "widthCm": 5,
              "heightCm": 5,
              "lengthCm": 5,
              "weightCm": 10,
              "volumeDm3": 0.125,
              "barcodes": [
                {
                  "code": "***********",
                  "type": "EAN13"
                }
              ]
            }
          ]
        }
      ]
    }
  }
}
```

## Response

The API returns an updated parcel object with:

| Field | Type | Description |
|-------|------|-------------|
| **parcelId** | string | The Instabee parcel ID |
| **status** | string | Updated status (e.g., "READY_FOR_PICKUP") |
| **pickupDetails** | object | Information about the scheduled pickup |
| **labelUrl** | string | URL to download the shipping label |
| **trackingNumber** | string | Tracking number for the parcel |
| **trackingUrl** | string | URL to track the parcel |
| **estimatedDelivery** | string | Estimated delivery date |

## Example Response

```json
{
  "parcelId": "IN123456789",
  "trackingNumber": "IB12345678SE",
  "trackingUrl": "https://tracking.instabee.com/t/IB12345678SE",
  "status": "READY_FOR_PICKUP",
  "labelUrl": "https://api.instabee.com/v1/parcels/IN123456789/label",
  "estimatedDelivery": "2025-05-16",
  "pickupDetails": {
    "date": "2025-05-15",
    "startTime": "13:00",
    "endTime": "17:00",
    "location": "Your Warehouse Address"
  }
}
```

## Integration Workflow

1. **Initial Order Creation**: First call the Post Purchase API to register the parcel
2. **Order Fulfillment**: Physically pack the order in your warehouse
3. **Packing Confirmation**: Call this Post Packing API with final dimensions and weight
4. **Label Generation**: Download the shipping label from the returned URL
5. **Pickup Preparation**: Prepare the parcel for pickup based on pickup details

## Error Handling

The API may return the following errors:

| Status Code | Error | Description | Solution |
|-------------|-------|-------------|----------|
| 400 | INVALID_REQUEST | Missing or invalid parameters | Check dimensions and weight |
| 404 | PARCEL_NOT_FOUND | Parcel ID not found | Verify the parcel ID matches your initial creation |
| 409 | ALREADY_PACKED | Parcel already marked as packed | No action needed, parcel is already confirmed |
| 422 | VALIDATION_ERROR | Validation errors (e.g., dimensions too large) | Adjust dimensions to meet constraints |

## Best Practices

1. **Accurate Measurements**: Always provide accurate dimensions and weight
2. **Prompt Confirmation**: Confirm packing as soon as possible after physical packing
3. **Error Handling**: Implement proper error handling for failed confirmations
4. **Label Management**: Download and store shipping labels immediately
5. **Status Tracking**: Monitor the parcel status via the Instabee API or dashboard

## Size Limitations

Each delivery method has maximum size and weight limitations:

| Delivery Type | Max Length (cm) | Max Width (cm) | Max Height (cm) | Max Weight (kg) |
|---------------|----------------|---------------|----------------|----------------|
| Locker - Small | 38 | 38 | 38 | 10 |
| Locker - Medium | 59 | 38 | 38 | 15 |
| Locker - Large | 59 | 59 | 38 | 20 |
| Home Delivery | 120 | 80 | 60 | 35 |

Exceeding these limitations may result in additional charges or delivery refusal.
