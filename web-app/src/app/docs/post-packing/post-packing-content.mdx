# Post Packing Flow

The Post Packing flow is the final confirmation step after the initial Post Purchase prebooking. This API is used to confirm that a parcel has been physically packed and is ready for pickup and delivery.

## Overview

**What is Post Packing?** This is the final confirmation after physical packing, providing actual dimensions and confirming the parcel is ready for pickup. It follows the Post Purchase flow which was the initial prebooking.

### API Endpoint

| Endpoint | Method | Content-Type |
|----------|--------|-------------|
| `api.instabee.com/orders` | PUT | application/json |

### When to Use

Call this endpoint after:
1. A parcel has been registered with the Post Purchase API
2. The order has been physically packed and prepared for shipping
3. The final dimensions and weight are known (if different from initial estimates)

### Integration Levels

Merchants can send information in different steps depending on their workflow:

- **Bare Minimum**: If everything was sent in Post Purchase, just confirm packing
- **Minimum**: Basic confirmation with essential parcel details
- **Maximum**: Complete order details with final dimensions and product information

## Examples

### Bare Minimum Integration

If everything was sent in the Post Purchase call, you only need:

```json
{
  "availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190",
  "parcelPackingConfirmed": true
}
```

### Minimum Integration

Basic confirmation with essential parcel details:

```json
{
  "parcelId": "PREFIX1234567890",
  "brand": "instabox",
  "product": "LOCKER_EXPRESS",
  "parcelPackingConfirmed": true,
  "recipient": {
    "name": "Test Testsson",
    "email": "<EMAIL>",
    "phone": "0701234567",
    "street": "Hälsingegatan 40",
    "street2": "Floor 10",
    "postalCode": "12345",
    "city": "Stockholm",
    "countryCode": "SE"
  },
  "deliveryOption": {
    "sort_code": "IN123"
  },
  "cart": {
    "orderNumber": "12345"
  }
}
```

## Key Benefits

1. **Pickup Scheduling**: Triggers pickup scheduling and route optimization
2. **Label Generation**: Provides shipping labels for the packed parcels
3. **Final Confirmation**: Confirms actual dimensions and weight for accurate delivery
4. **Status Updates**: Updates tracking status for customers

## Integration Workflow

1. **Post Purchase**: Initial prebooking reserves delivery capacity
2. **Physical Packing**: Pack the order in your warehouse
3. **Post Packing**: Confirm packing with this API (final dimensions, weight)
4. **Label Download**: Download shipping labels from the response
5. **Pickup**: Prepare parcels for scheduled pickup

## Next Steps

- **Complete Parameter Reference**: See the [Create Parcel API](/docs/post-packing/create-parcel) page for all available parameters
- **Initial Setup**: Start with the [Post Purchase API](/docs/post-purchase) for the prebooking
- **Integration Guide**: Check the [Authentication](/docs/authentication) section for API setup

## Quick Links

- [Create Parcel API Reference](/docs/post-packing/create-parcel) - Complete parameter documentation
- [Post Purchase API](/docs/post-purchase) - Initial prebooking before packing
- [Availability API](/docs/availability) - Check delivery options and get tokens
