import { Metadata } from "next";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Create Parcel - Post Packing | Instabee API Documentation",
  description: "Complete parameter reference for the Post Packing Create Parcel API",
}

export default function PostPackingCreateParcelPage() {
  return (
    <div className="docs-content">
      <h1>Create Parcel - Post Packing API</h1>

      <p>
        Complete parameter reference for confirming parcels in the Post Packing flow. This API confirms that a parcel
        has been physically packed and is ready for pickup and delivery.
      </p>

      <h2>API Endpoint</h2>

      <table>
        <thead>
          <tr>
            <th>Method</th>
            <th>Endpoint</th>
            <th>Content-Type</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td><code>PUT</code></td>
            <td><code>api.instabee.com/orders</code></td>
            <td><code>application/json</code></td>
          </tr>
        </tbody>
      </table>

      <h2>Integration Levels</h2>

      <p>You can integrate at different levels depending on your workflow:</p>

      <h3>Bare Minimum Integration</h3>

      <p>If everything was sent in the Post Purchase call, you only need:</p>

      <pre><code>{`{
  "availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190",
  "parcelPackingConfirmed": true
}`}</code></pre>

      <h3>Minimum Integration</h3>

      <p>Basic confirmation with essential parcel details:</p>

      <pre><code>{`{
  "parcelId": "PREFIX1234567890",
  "brand": "instabox",
  "product": "LOCKER_EXPRESS",
  "parcelPackingConfirmed": true,
  "recipient": {
    "name": "Test Testsson",
    "email": "<EMAIL>",
    "phone": "0701234567",
    "street": "Hälsingegatan 40",
    "street2": "Floor 10",
    "postalCode": "12345",
    "city": "Stockholm",
    "countryCode": "SE"
  },
  "deliveryOption": {
    "sort_code": "IN123"
  },
  "cart": {
    "orderNumber": "12345"
  }
}`}</code></pre>

      <h2>Root Parameters</h2>

      <table className="parameter-table">
        <thead>
          <tr>
            <th>Parameter</th>
            <th>Type</th>
            <th>Required</th>
            <th>Description</th>
            <th>Example</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td><code>parcelPackingConfirmed</code></td>
            <td>boolean</td>
            <td>required</td>
            <td>Confirms that the parcel has been packed and is ready for pickup</td>
            <td><code>true</code></td>
          </tr>
          <tr>
            <td><code>availabilityToken</code></td>
            <td>string</td>
            <td>conditional</td>
            <td>Token from Availability API or Post Purchase response</td>
            <td><code>"b4a9c200-c02a-4186-b3e8-6271a0be2190"</code></td>
          </tr>
          <tr>
            <td><code>parcelId</code></td>
            <td>string</td>
            <td>conditional</td>
            <td>Parcel identifier from Post Purchase response</td>
            <td><code>"PREFIX1234567890"</code></td>
          </tr>
          <tr>
            <td><code>brand</code></td>
            <td>string</td>
            <td>conditional</td>
            <td>Delivery brand (required if not sent in Post Purchase)</td>
            <td><code>"instabox"</code>, <code>"budbee"</code></td>
          </tr>
          <tr>
            <td><code>product</code></td>
            <td>string</td>
            <td>conditional</td>
            <td>Product type for delivery (required if not sent in Post Purchase)</td>
            <td><code>"LOCKER_EXPRESS"</code>, <code>"HOME_DELIVERY"</code></td>
          </tr>
        </tbody>
      </table>

      <h2>Parcel Dimensions (Optional)</h2>

      <p>If the actual dimensions differ from your initial estimates, you can update them:</p>

      <table className="parameter-table">
        <thead>
          <tr>
            <th>Parameter</th>
            <th>Type</th>
            <th>Required</th>
            <th>Description</th>
            <th>Example</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td><code>cart.parcel.heightCm</code></td>
            <td>number</td>
            <td>optional</td>
            <td>Final height in centimeters</td>
            <td><code>10</code></td>
          </tr>
          <tr>
            <td><code>cart.parcel.widthCm</code></td>
            <td>number</td>
            <td>optional</td>
            <td>Final width in centimeters</td>
            <td><code>10</code></td>
          </tr>
          <tr>
            <td><code>cart.parcel.lengthCm</code></td>
            <td>number</td>
            <td>optional</td>
            <td>Final length in centimeters</td>
            <td><code>10</code></td>
          </tr>
          <tr>
            <td><code>cart.parcel.weightGram</code></td>
            <td>number</td>
            <td>optional</td>
            <td>Final weight in grams</td>
            <td><code>2000</code></td>
          </tr>
          <tr>
            <td><code>cart.parcel.volumeDm3</code></td>
            <td>number</td>
            <td>optional</td>
            <td>Final volume in liters</td>
            <td><code>1</code></td>
          </tr>
        </tbody>
      </table>

      <h2>Response</h2>

      <p>The Post Packing API response includes:</p>

      <ul>
        <li><strong>Shipping Labels</strong> - Download URLs for shipping labels</li>
        <li><strong>Pickup Information</strong> - When and where the parcel will be collected</li>
        <li><strong>Tracking Information</strong> - Tracking numbers and status updates</li>
        <li><strong>Delivery Details</strong> - Final delivery information for the customer</li>
      </ul>

      <h2>Next Steps</h2>

      <ul>
        <li><Link href="/docs/post-packing" className="font-medium text-blue-600 underline underline-offset-4">Back to Post Packing Overview</Link></li>
        <li><Link href="/docs/post-purchase/create-parcel" className="font-medium text-blue-600 underline underline-offset-4">Post Purchase Create Parcel API</Link></li>
        <li><Link href="/docs/availability" className="font-medium text-blue-600 underline underline-offset-4">Availability API</Link></li>
      </ul>
    </div>
  );
}
