import { Metadata } from "next";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Create Parcel - Post Purchase | Instabee API Documentation",
  description: "Complete parameter reference for the Post Purchase Create Parcel API",
}

export default function CreateParcelPostPurchasePage() {
  return (
    <div className="docs-content">
      <h1>Create Parcel - Post Purchase API</h1>

      <p>
        Complete parameter reference for creating parcels in the Post Purchase flow. This API reserves delivery capacity
        immediately after a customer completes their purchase.
      </p>

      <h2>API Endpoint</h2>

      <table>
        <thead>
          <tr>
            <th>Method</th>
            <th>Endpoint</th>
            <th>Content-Type</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td><code>PUT</code></td>
            <td><code>api.instabee.com/orders</code></td>
            <td><code>application/json</code></td>
          </tr>
        </tbody>
      </table>

      <h2>Integration Levels</h2>

      <p>You can integrate at different levels depending on your business needs:</p>

      <h3>Bare Minimum Integration</h3>

      <p>The absolute minimum required to reserve delivery capacity:</p>

      <pre><code>{`{
  "availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190",
  "brand": "instabox",
  "product": "LOCKER_EXPRESS",
  "cart": {
    "orderNumber": "12345"
  },
  "deliveryOption": {
    "sort_code": "IN123"
  }
}`}</code></pre>

      <h3>Regular Integration</h3>

      <p>This is what most merchants send - basic recipient and delivery information:</p>

      <pre><code>{`{
  "availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190",
  "brand": "instabox",
  "product": "LOCKER_EXPRESS",
  "parcelId": "PREFIX1234567890",
  "recipient": {
    "name": "Test Testsson",
    "email": "<EMAIL>",
    "phone": "0701234567",
    "street": "Hälsingegatan 40",
    "street2": "Floor 10",
    "city": "Stockholm",
    "countryCode": "SE"
  },
  "deliveryOption": {
    "sort_code": "IN123"
  },
  "cart": {
    "checkoutId": "XBO12-2923048",
    "orderNumber": "12345"
  }
}`}</code></pre>

      <h2>Root Parameters</h2>

      <table className="parameter-table">
        <thead>
          <tr>
            <th>Parameter</th>
            <th>Type</th>
            <th>Required</th>
            <th>Description</th>
            <th>Example</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td><code>availabilityToken</code></td>
            <td>string</td>
            <td>conditional</td>
            <td>Token from Availability API (valid for 24 hours). Required if no orderNumber or checkoutId</td>
            <td><code>"b4a9c200-c02a-4186-b3e8-6271a0be2190"</code></td>
          </tr>
          <tr>
            <td><code>brand</code></td>
            <td>string</td>
            <td>required</td>
            <td>Delivery brand</td>
            <td><code>"instabox"</code>, <code>"budbee"</code></td>
          </tr>
          <tr>
            <td><code>product</code></td>
            <td>string</td>
            <td>required</td>
            <td>Product type for delivery</td>
            <td><code>"LOCKER_EXPRESS"</code>, <code>"HOME_DELIVERY"</code></td>
          </tr>
          <tr>
            <td><code>parcelId</code></td>
            <td>string</td>
            <td>optional</td>
            <td>Your unique parcel identifier. If not provided, Instabee will generate one</td>
            <td><code>"PREFIX1234567890"</code></td>
          </tr>
          <tr>
            <td><code>parcelGroupId</code></td>
            <td>string</td>
            <td>optional</td>
            <td>Group multiple parcels from the same order</td>
            <td><code>"123456789"</code></td>
          </tr>
          <tr>
            <td><code>brandId</code></td>
            <td>string</td>
            <td>optional</td>
            <td>Your brand identifier</td>
            <td><code>"Brand1"</code></td>
          </tr>
          <tr>
            <td><code>communicationName</code></td>
            <td>string</td>
            <td>optional</td>
            <td>Name to display in customer communications</td>
            <td><code>"That Merchant Name"</code></td>
          </tr>
        </tbody>
      </table>

      <h2>Recipient Object</h2>

      <table className="parameter-table">
        <thead>
          <tr>
            <th>Parameter</th>
            <th>Type</th>
            <th>Required</th>
            <th>Description</th>
            <th>Example</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td><code>name</code></td>
            <td>string</td>
            <td>required</td>
            <td>Recipient's full name</td>
            <td><code>"Test Testsson"</code></td>
          </tr>
          <tr>
            <td><code>email</code></td>
            <td>string</td>
            <td>required</td>
            <td>Recipient's email address</td>
            <td><code>"<EMAIL>"</code></td>
          </tr>
          <tr>
            <td><code>phone</code></td>
            <td>string</td>
            <td>conditional</td>
            <td>Recipient's phone number (can be optional with setting)</td>
            <td><code>"0701234567"</code></td>
          </tr>
          <tr>
            <td><code>street</code></td>
            <td>string</td>
            <td>required</td>
            <td>Street address</td>
            <td><code>"Hälsingegatan 40"</code></td>
          </tr>
          <tr>
            <td><code>street2</code></td>
            <td>string</td>
            <td>optional</td>
            <td>Additional address information</td>
            <td><code>"Floor 10"</code></td>
          </tr>
          <tr>
            <td><code>postalCode</code></td>
            <td>string</td>
            <td>required</td>
            <td>Postal code</td>
            <td><code>"12345"</code></td>
          </tr>
          <tr>
            <td><code>city</code></td>
            <td>string</td>
            <td>required</td>
            <td>City name</td>
            <td><code>"Stockholm"</code></td>
          </tr>
          <tr>
            <td><code>countryCode</code></td>
            <td>string</td>
            <td>required</td>
            <td>ISO 3166 alpha-2 country code</td>
            <td><code>"SE"</code>, <code>"NO"</code>, <code>"DK"</code></td>
          </tr>
          <tr>
            <td><code>ssn</code></td>
            <td>string</td>
            <td>optional</td>
            <td>Social security number (for age verification)</td>
            <td><code>"199004152012"</code></td>
          </tr>
        </tbody>
      </table>

      <h2>Delivery Option Object</h2>

      <table className="parameter-table">
        <thead>
          <tr>
            <th>Parameter</th>
            <th>Type</th>
            <th>Required</th>
            <th>Description</th>
            <th>Example</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td><code>sort_code</code></td>
            <td>string</td>
            <td>required</td>
            <td>Sort code from Availability API response</td>
            <td><code>"IN123"</code></td>
          </tr>
        </tbody>
      </table>

      <h2>Cart Object</h2>

      <table className="parameter-table">
        <thead>
          <tr>
            <th>Parameter</th>
            <th>Type</th>
            <th>Required</th>
            <th>Description</th>
            <th>Example</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td><code>checkoutId</code></td>
            <td>string</td>
            <td>conditional</td>
            <td>Checkout session ID. Required if no availabilityToken or orderNumber</td>
            <td><code>"XBO12-2923048"</code></td>
          </tr>
          <tr>
            <td><code>orderNumber</code></td>
            <td>string</td>
            <td>conditional</td>
            <td>Your order number. Required if no availabilityToken or checkoutId</td>
            <td><code>"12345"</code></td>
          </tr>
          <tr>
            <td><code>totalValueInCents</code></td>
            <td>number</td>
            <td>optional</td>
            <td>Total order value in cents</td>
            <td><code>12300</code></td>
          </tr>
          <tr>
            <td><code>totalWeightGram</code></td>
            <td>number</td>
            <td>optional</td>
            <td>Total weight in grams</td>
            <td><code>30000</code></td>
          </tr>
        </tbody>
      </table>

      <h2>Next Steps</h2>

      <ul>
        <li><Link href="/docs/post-purchase" className="font-medium text-blue-600 underline underline-offset-4">Back to Post Purchase Overview</Link></li>
        <li><Link href="/docs/post-packing/create-parcel" className="font-medium text-blue-600 underline underline-offset-4">Post Packing Create Parcel API</Link></li>
        <li><Link href="/docs/availability" className="font-medium text-blue-600 underline underline-offset-4">Availability API</Link></li>
      </ul>
    </div>
  );
}
