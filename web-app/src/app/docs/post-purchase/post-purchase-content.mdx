# Post Purchase Flow

The Post Purchase flow (also known as Create Parcel or Order Create API) is the first step in the Instabee delivery process. This is a prebooking that reserves delivery capacity immediately after a customer completes their purchase.

## Overview

**What is Post Purchase?** This is like a prebooking - it reserves delivery capacity and creates the initial order in the Instabee system. Merchants can send information in different steps, with the Post Packing flow being the final confirmation after physical packing.

### API Endpoint

| Endpoint | Method | Content-Type |
|----------|--------|-------------|
| `api.instabee.com/orders` | PUT | application/json |

### When to Use

Call this endpoint immediately after:
1. Customer completes their purchase in your checkout system
2. You have selected a delivery option using the Availability API
3. You want to reserve delivery capacity before packing

### Integration Levels

You can integrate at different levels depending on your business needs:

- **Bare Minimum**: Just an availability token or order reference
- **Regular**: Basic recipient and delivery information (most common)
- **Maximum**: Complete order details including products, dimensions, and special services

## Examples

### Bare Minimum Integration

If you only want to reserve capacity with minimal information:

```json
{
  "availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190",
  "brand": "instabox",
  "product": "LOCKER_EXPRESS",
  "cart": {
    "orderNumber": "12345"
  },
  "deliveryOption": {
    "sort_code": "IN123"
  }
}
```

### Regular Integration

This is what most merchants send - basic recipient and delivery information:

```json
{
  "availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190",
  "brand": "instabox",
  "product": "LOCKER_EXPRESS",
  "parcelId": "PREFIX1234567890",
  "recipient": {
    "name": "Test Testsson",
    "email": "<EMAIL>",
    "phone": "0701234567",
    "street": "Hälsingegatan 40",
    "street2": "Floor 10",
    "city": "Stockholm",
    "countryCode": "SE"
  },
  "deliveryOption": {
    "sort_code": "IN123"
  },
  "cart": {
    "checkoutId": "XBO12-2923048",
    "orderNumber": "12345"
  }
}
```

## Key Benefits

1. **Capacity Reservation**: Ensures delivery slots are reserved for your customers
2. **Flexible Integration**: Send as much or as little information as you have available
3. **Early Tracking**: Customers get tracking information immediately after purchase
4. **Optimized Logistics**: Helps Instabee plan pickup and delivery routes

## Next Steps

- **Complete Parameter Reference**: See the [Create Parcel API](/docs/post-purchase/create-parcel) page for all available parameters
- **After Packing**: Use the [Post Packing API](/docs/post-packing) to confirm when parcels are ready for pickup
- **Integration Guide**: Check the [Authentication](/docs/authentication) section for API setup

## Quick Links

- [Create Parcel API Reference](/docs/post-purchase/create-parcel) - Complete parameter documentation
- [Availability API](/docs/availability) - Check delivery options before creating parcels
- [Post Packing API](/docs/post-packing) - Confirm parcels after physical packing
