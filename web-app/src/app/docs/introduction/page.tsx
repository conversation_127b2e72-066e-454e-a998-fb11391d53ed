import { Metadata } from "next";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Introduction | Instabee API Documentation",
  description: "Getting started with the Instabee API",
}

export default function IntroductionPage() {
  return (
    <div className="docs-content">
      <h1 id="introduction">
        Introduction to Instabee API
      </h1>

      <p>
        Welcome to the Instabee API documentation. This comprehensive guide will help you integrate with our delivery platform
        and provide your customers with an exceptional delivery experience across multiple countries and delivery methods.
      </p>

      <h2 id="overview">
        Overview
      </h2>

      <p>The Instabee API is a powerful platform that enables you to:</p>

      <ul>
        <li><strong>Check delivery availability</strong> - Validate postal codes and get available delivery slots</li>
        <li><strong>Find nearby lockers</strong> - Locate Instabox and Budbee lockers for convenient pickup</li>
        <li><strong>Create parcels</strong> - Register orders immediately after purchase to reserve capacity</li>
        <li><strong>Manage post-packing</strong> - Confirm when parcels are ready for pickup with final dimensions</li>
        <li><strong>Track deliveries</strong> - Monitor delivery status and provide updates to customers</li>
      </ul>

      <h2 id="api-architecture">
        API Architecture
      </h2>

      <p>The Instabee API follows a RESTful design with the following key characteristics:</p>

      <ul>
        <li><strong>JSON-based</strong> - All requests and responses use JSON format</li>
        <li><strong>HTTP methods</strong> - Uses standard HTTP methods (GET, POST, PUT)</li>
        <li><strong>Stateless</strong> - Each request contains all necessary information</li>
        <li><strong>Consistent structure</strong> - Predictable response formats across all endpoints</li>
      </ul>

      <h2 id="getting-started">
        Getting Started
      </h2>

      <p>To start using the Instabee API, follow these steps:</p>

      <ol>
        <li><strong>Register for an API key</strong> - Contact our sales team or sign up through the developer portal</li>
        <li><strong>Set up authentication</strong> - Learn how to securely authenticate your requests</li>
        <li><strong>Test with sandbox</strong> - Use our sandbox environment to test your integration</li>
        <li><strong>Understand the flow</strong> - Familiarize yourself with the typical integration workflow</li>
        <li><strong>Go live</strong> - Deploy your integration to production</li>
      </ol>

      <h2 id="integration-workflow">
        Typical Integration Workflow
      </h2>

      <ol>
        <li><strong>Availability Check</strong> - Customer enters postal code, you check if delivery is available</li>
        <li><strong>Display Options</strong> - Show available delivery slots or nearby lockers</li>
        <li><strong>Customer Selection</strong> - Customer chooses their preferred delivery option</li>
        <li><strong>Post Purchase</strong> - After payment, create a parcel to reserve delivery capacity</li>
        <li><strong>Fulfillment</strong> - Pack the order in your warehouse</li>
        <li><strong>Post Packing</strong> - Confirm the parcel is ready with final dimensions</li>
        <li><strong>Pickup & Delivery</strong> - Instabee handles pickup and delivery to the customer</li>
      </ol>

      <h2 id="api-base-url">
        API Base URL
      </h2>

      <p>All API requests should be made to the following base URL:</p>

      <pre><code>https://api.instabee.com/v1</code></pre>

      <h2 id="supported-countries">
        Supported Countries
      </h2>

      <p>Instabee currently operates in the following countries:</p>

      <ul>
        <li><strong>Sweden (SE)</strong> - Full coverage with both home delivery and lockers</li>
        <li><strong>Norway (NO)</strong> - Major cities and surrounding areas</li>
        <li><strong>Denmark (DK)</strong> - Copenhagen and major urban areas</li>
        <li><strong>Finland (FI)</strong> - Helsinki and surrounding regions</li>
      </ul>

      <h2 id="next-steps">
        Next Steps
      </h2>

      <p>
        Ready to get started? Continue reading to learn about <Link href="/docs/authentication" className="font-medium text-blue-600 underline underline-offset-4">Authentication</Link> or jump directly to specific endpoints:
      </p>

      <ul>
        <li>
          <Link href="/docs/availability" className="font-medium text-blue-600 underline underline-offset-4">Availability API</Link> - Check delivery options and find lockers
        </li>
        <li>
          <Link href="/docs/post-purchase" className="font-medium text-blue-600 underline underline-offset-4">Post Purchase API</Link> - Create parcels and reserve capacity
        </li>
        <li>
          <Link href="/docs/post-packing" className="font-medium text-blue-600 underline underline-offset-4">Post Packing API</Link> - Confirm parcels are ready for pickup
        </li>
      </ul>

      <blockquote>
        <strong>Need help?</strong> Our technical support team is available to assist with your integration.
        Contact us at <a href="mailto:<EMAIL>" className="font-medium text-blue-600 underline underline-offset-4"><EMAIL></a>
      </blockquote>
    </div>
  );
}
