import { Metadata } from "next";
import Link from "next/link";
import { docsConfig } from "@/config/docs-navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export const metadata: Metadata = {
  title: "Instabee API Documentation",
  description: "Documentation for the Instabee API",
}

export default function DocsPage() {
  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h1 className="text-3xl font-bold tracking-tight">Instabee API Documentation</h1>
        <p className="text-lg text-muted-foreground">
          Welcome to the Instabee API documentation. Integrate with our delivery platform to provide seamless shipping experiences for your customers.
        </p>

        <div className="rounded-lg border bg-card p-6">
          <h2 className="text-xl font-semibold mb-3">API Overview</h2>
          <p className="text-muted-foreground mb-4">
            The Instabee API provides two main flows for creating and managing deliveries:
          </p>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <h3 className="font-medium">Post Purchase (Prebooking)</h3>
              <p className="text-sm text-muted-foreground">
                Reserve delivery capacity immediately after customer purchase. Also known as Create Parcel or Order Create API.
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium">Post Packing (Confirmation)</h3>
              <p className="text-sm text-muted-foreground">
                Confirm when parcels are physically packed and ready for pickup with final dimensions.
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {docsConfig.sidebarNav.map((item) => (
          <Card key={item.title} className="flex flex-col">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">{item.title}</CardTitle>
              <CardDescription>
                {item.title === "Introduction" && "Get started with the Instabee API and understand the integration flow"}
                {item.title === "Authentication" && "Learn how to authenticate with the API using API keys"}
                {item.title === "Availability" && "Check delivery availability and options for customer addresses"}
                {item.title === "Post Purchase" && "Create parcels after purchase (prebooking) - also known as Order Create API"}
                {item.title === "Post Packing" && "Confirm parcels after packing with final dimensions and weight"}
              </CardDescription>
            </CardHeader>
            <CardContent className="flex-1">
              {item.href && (
                <Link
                  href={item.href}
                  className="inline-flex items-center rounded-md bg-primary px-3 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90"
                >
                  View Documentation
                </Link>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
