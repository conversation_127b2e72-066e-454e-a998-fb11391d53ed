import React from "react";
import { DocsSidebar } from "@/components/docs-sidebar";
import { DocsTOC } from "@/components/docs-toc";
import { SidebarProvider } from "@/components/ui/sidebar";

interface DocsLayoutProps {
  children: React.ReactNode;
}

export default function DocsLayout({ children }: DocsLayoutProps) {
  return (
    <SidebarProvider>
      <div className="flex flex-col min-h-screen">
        {/* Full-height layout without header */}
        <div className="flex flex-1 overflow-hidden">
          <DocsSidebar />
          <div className="flex-1 overflow-auto">
            <div className="py-6 md:py-10 px-6 md:px-8 max-w-4xl mx-auto">
              {children}
            </div>
          </div>
          <div className="hidden lg:block w-64 overflow-auto p-8 border-l">
            <div className="sticky top-6">
              <DocsTOC />
            </div>
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
}
