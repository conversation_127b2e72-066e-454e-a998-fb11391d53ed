import { Metadata } from "next";
import { MDXRemote } from "next-mdx-remote/rsc";
import remarkGfm from 'remark-gfm';

export const metadata: Metadata = {
  title: "Authentication | Instabee API Documentation",
  description: "How to authenticate with the Instabee API",
}

import fs from 'fs';
import path from 'path';

function getAuthContent() {
  const filePath = path.join(process.cwd(), 'src/app/docs/authentication/authentication-content.mdx');
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error('Error loading authentication content:', error);
    return '';
  }
}

export default async function AuthenticationPage() {
  const content = await getAuthContent();
  return (
    <div className="docs-content">
      <MDXRemote
        source={content}
        options={{
          mdxOptions: {
            remarkPlugins: [remarkGfm],
            format: 'mdx'
          }
        }}
      />
    </div>
  );
}
