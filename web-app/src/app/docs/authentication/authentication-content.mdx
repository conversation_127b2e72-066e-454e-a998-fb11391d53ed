# Authentication

To use the Instabee API, you need to authenticate your requests using API keys. This guide explains how to obtain and use your API keys securely.

## API Keys

Instabee uses API keys to authenticate requests. You can view and manage your API keys in the Instabee Dashboard.

### Obtaining an API Key

1. Log in to your [Instabee Dashboard](https://dashboard.instabee.com)
2. Navigate to API > API Keys
3. Click "Create New API Key"
4. Name your key based on its purpose (e.g., "Production", "Development")
5. Select the appropriate permissions
6. Copy the key immediately - it will only be shown once!

Your API keys carry many privileges, so be sure to keep them secure. Do not share your API keys in publicly accessible areas such as GitHub, client-side code, or in your frontend application.

## Authentication Methods

All API requests must include your API key in the request headers:

```
X-Instabee-API-Key: your_api_key_here
```

## Example Request

Here's an example of how to include your API key in a request:

```javascript
const response = await fetch('https://api.instabee.com/v1/availability', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-Instabee-API-Key': 'your_api_key_here'
  },
  body: JSON.stringify({
    // Request body here
  })
});
```

## API Key Security

To keep your API keys secure:

1. Store API keys in environment variables
2. Do not hardcode API keys in your application code
3. Restrict API key access to only the required endpoints
4. Regularly rotate your API keys
5. Use separate keys for development and production environments
6. Implement proper error handling to avoid exposing keys in error messages

## API Key Types

Instabee offers different types of API keys:

| Key Type | Purpose | Permissions |
|----------|---------|-------------|
| **Production** | For live integrations | Full access to production environment |
| **Sandbox** | For testing | Limited to test environment, no real deliveries |
| **Read-Only** | For monitoring | Can only retrieve data, no creation/modification |

## Rate Limiting

API requests are subject to rate limiting to ensure fair usage. The current rate limits are:

| API Endpoint | Rate Limit |
|--------------|------------|
| Standard endpoints | 60 requests per minute |
| Batch operations | 10 requests per minute |
| Webhook registration | 5 requests per minute |

If you exceed these limits, your requests will receive a 429 (Too Many Requests) response with a header indicating when you can try again.

## Error Handling

When authentication fails, you will receive one of the following error responses:

| Status Code | Description | Resolution |
|-------------|-------------|------------|
| 401 | Invalid API key | Check that your API key is correct and properly formatted |
| 403 | Insufficient permissions | Request additional permissions for your API key |

## Next Steps

Now that you understand authentication, you can explore the API endpoints:

- [Availability](/docs/availability)
- [Post Purchase](/docs/post-purchase)
- [Post Packing](/docs/post-packing)
