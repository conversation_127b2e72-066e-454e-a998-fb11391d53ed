# Availability Request Parameters

## Endpoints Parameters

| Parameter | Endpoint | Type | Header | Comment |
|--------------------------------|-----------------------------------------------------------------------|------|---------------------------------|---------------------------------|
| Get Locker Per Postal Code | `api.instabee.com/availability/postal-codes/12345/lockers` | POST | Auth key, version, content header | |
| Get Locker Per Country | `api.instabee.com/availability/countries/se/lockers` | GET | Auth key, version, content header | |
| Validate Postal Code or Get Delivery Slots | `api.instabee.com/availability/postal-codes/12345/home-deliveries` | POST | Auth key, version, content header | |
| Get Postal Codes Per Country | `api.instabee.com/availability/countries/se/home-delivery-postal-codes` | GET | Auth key, version, content header | |

## Query Parameters

| Parameter | Type | Required | Description | Constraints | Enum | Also query param | Comment |
|---------------------|--------|-----------|---------------------------------------------------------|-----------------------------------------------|------------------------------------|------------------|-----------------------------------------------------|
| **brand** | string | optional | Define brand of the delivery options | `instabox` or `budbee` | N/A | | |
| **product** | string | optional | Define the product of the delivery options | Will be defined by tech sales in the integration process | | | The idea is to use this for SME setups in the future |
| **countryCode** | string | required* | Recipient country code. *Can be sent in the body instead | ISO 3166 alpha 2 | | | |
| **deliverySlotCount** | number | optional | Define the upcoming number of delivery slots to be returned. | Minimum 5, Maximum 10 | | | |
| **deliverySlotsByDate** | string | optional | Define specific dates to receive delivery slots between. "2025-06-01,2025-06-08" | Maximum delivery options will be 10. ISO-8601-date YYYY-MM-DD | | | |
| **email** | string | optional | Recipient email address | | | | |
| **address** | string | optional | Recipient address | `%20` should be used for spaces | | | |
| **city** | string | optional | Recipient city | | | | |

## Request Body

| Parameter | Type | Required | Description | Constraints | Enum | Also query param | Comment |
|---------------|--------|-----------|------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------|------|------------------|-----------------------------------------------------|
| **brand** | string | optional | Define brand of the delivery options | `instabox` or `budbee` | N/A | | |
| **product** | string | optional | Define the product of the delivery options | Will be defined by tech sales in the integration process | | | The idea is to use this for SME setups in the future |
| **countryCode** | string | required* | Recipient country code. *Can be sent in the query instead | ISO 3166 alpha 2 | | | |
| **deliverySlotCount** | number | optional | Define the upcoming number of delivery slots to be returned. | Minimum 5, Maximum 10 | | | |
| **deliverySlotsByDate** | string | optional | Define specific dates to receive delivery slots between. "2025-06-01,2025-06-08" | Maximum delivery options will be 10. ISO-8601-date YYYY-MM-DD | | | |
| **email** | string | optional | Recipient email address | | | | |
| **address** | string | optional | Recipient address | `%20` should be used for spaces | | | |
| **city** | string | optional | Recipient city | | | | |

#### cart.parcel.products

| Parameter | Type | Required | Description | Constraints | Enum | Comment |
|---------------|--------|-----------|--------------------------------------------------------|-------------|------|-------------------------------------------|
| **name** | string | optional | Name of the product | | | |
| **quantity** | string | optional | Quantity of the product | | | |
| **productId** | string | optional | The unique identifier of the product e.g EAN number | | | Not to be confused with (product) serial number. |
| **details** | string | optional | Details of the product | | | |
| **temperature** | string | optional | Temperature restraints of the product | | | |

#### cart.parcel.products.details

| Parameter | Type | Required | Description | Constraints | Enum | Comment |
|--------------|--------|-----------|-------------------------------------------------|-------------|------|--------|
| **productType** | string | optional | Type of product, e.g Toys | | | |
| **imgUrl** | string | optional | An URL link to the image of the product | | | |
| **category** | string | optional | The category of the product e.g Plushie | | | |
| **brand** | string | optional | The brand of the product | | | |
| **description** | string | optional | The description of the product | | | |
| **price** | object | optional | The price of the product for a single item | | | |

#### cart.parcel.products.details.price

| Parameter | Type | Required | Description | Constraints | Enum | Comment |
|-------------------|--------|-----------|---------------------------------------------------|-------------|------|--------|
| **priceInCents** | number | optional | Price of a single item in cents without discount | | | |
| **taxRateInCents** | number | optional | The tax rate of the item in cents | | | % |
| **discountRateInCents** | number | optional | The active discount set on the product in cents | | | |
| **currency** | string | optional | The currency of the checkout session | | | |

#### cart.parcel.products.details.temperature

| Parameter | Type | Required | Description | Constraints | Enum | Comment |
|-------|--------|-----------|----------------------------------------------------------------------------------------------------|--------------------|----|--------|
| **min** | number | optional | The minimum temperature the product can experience during transport and at the delivery option before pickup. Temperature in celsius | Min 8- Max 25 | | |
| **max** | number | optional | The maximum temperature the product can experience during transport and at the delivery option before pickup. Temperature in celsius | | | |

#### cart.parcel.products.packages

| Parameter | Type | Required | Description | Constraints | Enum | Comment |
|----------------|--------|-----------|--------------------------------------------------|-------------|------|--------|
| **heightMm** | string | optional | The height of the individual product package in mm | | | |
| **widthMm** | string | optional | The width of the individual product package in mm | | | |
| **lengthMm** | string | optional | The length of the individual product package in mm | | | |
| **weightGram** | string | optional | The weight of the individual product package in gram | | | |
| **volumeDm3** | string | optional | The volume of the individual product package in liters | | | |
| **barcodes** | object | optional | The barcodes of the individual product | | | |

#### cart.parcel.products.packages.barcodes

| Parameter | Type | Required | Description | Constraints | Enum | Comment |
|--------|--------|-----------|----------------------------------------------|-------------|--------|---------|
| **code** | string | optional | The content of the barcode e.g 123-456-789 | | | |
| **type** | string | optional | The barcode type e.g EAN13 | | | |
