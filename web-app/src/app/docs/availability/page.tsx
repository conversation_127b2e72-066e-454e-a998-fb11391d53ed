import { <PERSON>ada<PERSON> } from "next";
import { MDXRemote } from "next-mdx-remote/rsc";
import remarkGfm from 'remark-gfm';
import fs from 'fs';
import path from 'path';

export const metadata: Metadata = {
  title: "Availability | Instabee API Documentation",
  description: "Check delivery availability for addresses",
}

// Load MDX content from a file
async function getAvailabilityContent() {
  const filePath = path.join(process.cwd(), 'src/app/docs/availability/availability-content.mdx');
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error('Error loading availability content:', error);
    return '# Error loading availability documentation';
  }
}

export default async function AvailabilityPage() {
  const content = await getAvailabilityContent();
  return (
    <div className="docs-content">
      <MDXRemote
        source={content}
        options={{
          mdxOptions: {
            remarkPlugins: [remarkGfm],
            format: 'mdx'
          }
        }}
      />
    </div>
  );
}
