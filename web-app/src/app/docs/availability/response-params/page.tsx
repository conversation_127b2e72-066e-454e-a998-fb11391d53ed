import { Metadata } from "next";
import { MDXRemote } from "next-mdx-remote/rsc";
import remarkGfm from 'remark-gfm';
import path from 'path';
import fs from 'fs';

export const metadata: Metadata = {
  title: "Availability Response Parameters | Instabee API Documentation",
  description: "Response parameters from the Availability API",
}

const markdownContent = `
# Availability Response Parameters

This page documents the structure and parameters returned by the Availability API. The response includes information about delivery options, pricing, and available delivery slots.

## Response Structure

The response from the Availability API includes the following main sections:

- **deliveryOptions** - Available delivery methods
- **locker** - Information about locker delivery options
- **homeDelivery** - Information about home delivery options
- **deliverySlots** - Available delivery time slots
- **pricing** - Cost information for each delivery option

## Response Parameters

| Parameter | Type | Description | Comment |
|-----------|------|-------------|---------|
| **deliveryOptions** | array | List of available delivery options |  |
| **homeDelivery** | object | Home delivery information | Only present if home delivery is available |
| **locker** | object | Locker delivery information | Only present if locker delivery is available |
| **pricing** | object | Pricing information for delivery options |  |
| **serviceablePostalCode** | boolean | Indicates if the postal code is serviceable |  |

### Delivery Options

Each delivery option in the **deliveryOptions** array contains:

| Parameter | Type | Description | Comment |
|-----------|------|-------------|---------|
| **id** | string | Unique identifier for the delivery option |  |
| **name** | string | Display name of the delivery option |  |
| **description** | string | Detailed description of the delivery option |  |
| **type** | string | Type of delivery (e.g., "locker", "home") |  |
| **brand** | string | Brand offering the delivery (e.g., "instabox", "budbee") |  |
| **price** | number | Cost of the delivery option |  |
| **currency** | string | Currency of the price |  |
| **estimatedDeliveryTime** | object | Estimated delivery time information |  |

### Delivery Slots

For home delivery options, delivery slots provide available time windows:

| Parameter | Type | Description | Comment |
|-----------|------|-------------|---------|
| **id** | string | Unique identifier for the slot |  |
| **startTime** | string | Start time of the delivery window (ISO 8601) |  |
| **endTime** | string | End time of the delivery window (ISO 8601) |  |
| **date** | string | Date of the delivery (YYYY-MM-DD) |  |

### Locker Information

For locker delivery options:

| Parameter | Type | Description | Comment |
|-----------|------|-------------|---------|
| **id** | string | Unique identifier for the locker |  |
| **name** | string | Name of the locker location |  |
| **address** | object | Address information for the locker |  |
| **coordinates** | object | Geographic coordinates of the locker |  |
| **openingHours** | array | Opening hours of the locker location |  |

## Example Response

\`\`\`json
{
  "serviceablePostalCode": true,
  "deliveryOptions": [
    {
      "id": "locker-123",
      "name": "Locker Delivery",
      "description": "Delivery to a locker near you",
      "type": "locker",
      "brand": "instabox",
      "price": 49,
      "currency": "SEK",
      "estimatedDeliveryTime": {
        "min": "1 day",
        "max": "2 days"
      }
    },
    {
      "id": "home-456",
      "name": "Home Delivery",
      "description": "Delivery to your doorstep",
      "type": "home",
      "brand": "budbee",
      "price": 79,
      "currency": "SEK",
      "estimatedDeliveryTime": {
        "min": "1 day",
        "max": "1 day"
      }
    }
  ],
  "locker": {
    "id": "locker-123",
    "name": "Central Mall",
    "address": {
      "street": "Main Street 123",
      "postalCode": "12345",
      "city": "Stockholm",
      "countryCode": "SE"
    },
    "coordinates": {
      "latitude": 59.3293,
      "longitude": 18.0686
    },
    "openingHours": [
      {
        "day": "monday",
        "open": "08:00",
        "close": "22:00"
      }
    ]
  },
  "homeDelivery": {
    "availableSlots": 5,
    "deliverySlots": [
      {
        "id": "slot-789",
        "date": "2025-06-01",
        "startTime": "2025-06-01T10:00:00Z",
        "endTime": "2025-06-01T14:00:00Z"
      }
    ]
  },
  "pricing": {
    "locker": {
      "price": 49,
      "currency": "SEK"
    },
    "homeDelivery": {
      "price": 79,
      "currency": "SEK"
    }
  }
}
\`\`\`
`;

export default function AvailabilityResponseParamsPage() {
  return (
    <div className="space-y-4">
      <div className="mdx max-w-full break-words">
        <MDXRemote
          source={markdownContent}
          options={{
            mdxOptions: {
              remarkPlugins: [remarkGfm],
              format: 'mdx'
            }
          }}
        />
      </div>
    </div>
  );
}

