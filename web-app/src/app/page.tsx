import Image from "next/image";
import Link from "next/link";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Instabee API Documentation",
  description: "Documentation for the Instabee API",
}

export default function Home() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-8">
      <main className="flex w-full max-w-5xl flex-col items-center justify-center space-y-12">
        <div className="text-center">
          <h1 className="text-5xl font-bold mb-4">Instabee API</h1>
          <p className="text-xl text-muted-foreground mb-8">
            Comprehensive documentation for the Instabee delivery platform API
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 w-full">
          <div className="flex flex-col p-6 bg-card rounded-lg border shadow-sm">
            <h2 className="text-2xl font-bold mb-2">Getting Started</h2>
            <p className="text-muted-foreground mb-4">
              Learn how to integrate with the Instabee API and provide your customers with an exceptional delivery experience.
            </p>
            <Link 
              href="/docs/introduction"
              className="mt-auto inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90"
            >
              Get Started
            </Link>
          </div>

          <div className="flex flex-col p-6 bg-card rounded-lg border shadow-sm">
            <h2 className="text-2xl font-bold mb-2">API Reference</h2>
            <p className="text-muted-foreground mb-4">
              Comprehensive documentation for all available API endpoints, request parameters, and response schemas.
            </p>
            <Link 
              href="/docs"
              className="mt-auto inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90"
            >
              View Documentation
            </Link>
          </div>
        </div>

        <div className="flex flex-col p-6 bg-card rounded-lg border shadow-sm w-full">
          <h2 className="text-2xl font-bold mb-2">Key Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4">
              <h3 className="text-lg font-bold mb-2">Availability API</h3>
              <p className="text-muted-foreground">Check delivery availability for addresses and find nearby lockers</p>
            </div>
            <div className="p-4">
              <h3 className="text-lg font-bold mb-2">Post Purchase</h3>
              <p className="text-muted-foreground">Create parcels and reserve capacity after customer purchase</p>
            </div>
            <div className="p-4">
              <h3 className="text-lg font-bold mb-2">Post Packing</h3>
              <p className="text-muted-foreground">Update parcels after packing and prepare for shipping</p>
            </div>
          </div>
        </div>
      </main>
      <footer className="mt-12 text-center text-sm text-muted-foreground">
        <p> {new Date().getFullYear()} Instabee. All rights reserved.</p>
      </footer>
    </div>
  );
}
