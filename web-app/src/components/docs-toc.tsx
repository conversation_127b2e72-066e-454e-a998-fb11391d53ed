'use client';

import React, { useEffect, useState } from 'react';
import { usePathname, useSearchParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { cn } from '@/lib/utils';

interface TOCItem {
  id: string;
  text: string;
  level: number;
  children: TOCItem[];
}

export function DocsTOC() {
  const [headings, setHeadings] = useState<TOCItem[]>([]);
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();
  const activeSection = searchParams.get('section');

  // Extract headings from the document
  useEffect(() => {
    const extractHeadings = () => {
      const headingElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
      const toc: TOCItem[] = [];
      const headingsByLevel: Record<number, TOCItem[]> = {
        1: toc, // Root level
        2: [], // h2
        3: [], // h3
        4: [], // h4
        5: [], // h5
        6: [], // h6
      };

      headingElements.forEach((el) => {
        const level = parseInt(el.tagName[1]);
        const id = el.id;
        const text = el.textContent || '';
        
        // Skip headings without IDs or with empty text
        if (!id || !text.trim()) return;
        
        const item: TOCItem = { id, text, level, children: [] };
        
        // Add to appropriate level
        if (level === 1) {
          toc.push(item);
        } else if (level === 2) {
          toc.push(item);
        } else {
          // Find parent heading
          let parentLevel = level - 1;
          while (parentLevel >= 2) {
            if (headingsByLevel[parentLevel].length > 0) {
              const parent = headingsByLevel[parentLevel][headingsByLevel[parentLevel].length - 1];
              parent.children.push(item);
              break;
            }
            parentLevel--;
          }
          
          // If no parent found, add to root
          if (parentLevel < 2) {
            toc.push(item);
          }
        }
        
        // Update the headings by level trackers
        headingsByLevel[level].push(item);
      });
      
      return toc;
    };
    
    // Extract headings after a delay to ensure content is loaded
    const timeoutId = setTimeout(() => {
      const extractedHeadings = extractHeadings();
      setHeadings(extractedHeadings);
    }, 500);
    
    return () => clearTimeout(timeoutId);
  }, [pathname]);

  // Scroll to section when query parameter changes
  useEffect(() => {
    if (activeSection) {
      const element = document.getElementById(activeSection);
      if (element) {
        // Add a small delay to ensure the page has rendered
        setTimeout(() => {
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest'
          });
        }, 100);
      }
    }
  }, [activeSection]);

  if (headings.length === 0) {
    return null;
  }

  return (
    <div className="space-y-1">
      <p className="font-medium text-sm text-muted-foreground mb-2">On this page</p>
      <TocItems items={headings} activeSection={activeSection} pathname={pathname} />
    </div>
  );
}

interface TocItemsProps {
  items: TOCItem[];
  activeSection: string | null;
  pathname: string;
  level?: number;
}

function TocItems({ items, activeSection, pathname, level = 0 }: TocItemsProps) {
  const handleClick = (e: React.MouseEvent, itemId: string) => {
    e.preventDefault();

    // Update URL with query parameter
    const url = new URL(window.location.href);
    url.searchParams.set('section', itemId);
    window.history.pushState({}, '', url.toString());

    // Scroll to element
    const element = document.getElementById(itemId);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      });
    }
  };

  return (
    <ul className={cn("pl-3 space-y-1", level === 0 && "pl-0")}>
      {items.map((item, index) => (
        <li key={`${item.id}-${index}`} className="text-sm leading-snug">
          <a
            href={`${pathname}?section=${item.id}`}
            onClick={(e) => handleClick(e, item.id)}
            className={cn(
              "block py-1 text-muted-foreground hover:text-foreground transition-colors cursor-pointer",
              activeSection === item.id && "text-amber-500 font-medium",
              // Adjust indentation based on heading level
              item.level === 2 ? "pl-0" :
              item.level === 3 ? "pl-2" :
              item.level === 4 ? "pl-4" :
              item.level === 5 ? "pl-6" :
              item.level === 6 ? "pl-8" : ""
            )}
          >
            {item.text}
          </a>
          {item.children.length > 0 && (
            <TocItems
              items={item.children}
              activeSection={activeSection}
              pathname={pathname}
              level={level + 1}
            />
          )}
        </li>
      ))}
    </ul>
  );
}
