"use client";

import Link from "next/link";
import { usePathname, useSearchParams } from "next/navigation";
import { useState } from "react";
import { cn } from "@/lib/utils";
import { NavItem, docsConfig } from "@/config/docs-navigation";
import { Sidebar, <PERSON>barFooter, SidebarHeader, SidebarContent } from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { SearchButton } from "@/components/search/search-button";
import { SearchDialog } from "@/components/search/search-dialog";
import { SearchProvider } from "@/components/search/search-provider";
import { ChevronDown, ChevronRight } from "lucide-react";

interface DocsSidebarProps {
  className?: string;
}

export function DocsSidebar({ className }: DocsSidebarProps) {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Initialize with some deeply nested items collapsed by default
  const getInitialCollapsedState = () => {
    const initialCollapsed = new Set<string>();
    // Collapse deeply nested items by default (level 4 and above)
    const deeplyNestedItems = [
      "/docs/availability?section=cart",
      "/docs/availability?section=sender",
      "/docs/availability?section=recipient",
      "/docs/availability?section=options",
    ];
    deeplyNestedItems.forEach(item => initialCollapsed.add(item));
    return initialCollapsed;
  };

  const [collapsedItems, setCollapsedItems] = useState<Set<string>>(getInitialCollapsedState);

  return (
    <SearchProvider>
      <Sidebar className={cn("border-r bg-background", className)}>
        <SidebarHeader className="flex flex-col gap-3 h-auto py-4 border-b px-4">
          <Link href="/" className="flex items-center gap-2 font-semibold w-full">
            <span className="text-xl font-bold">Instabee API</span>
          </Link>
          <div className="w-full">
            <SearchButton />
            <SearchDialog />
          </div>
        </SidebarHeader>
      <SidebarContent>
        <div className="px-4 py-2">
          <h4 className="mb-2 text-sm font-semibold text-muted-foreground">Documentation</h4>
          <div className="space-y-1">
            {docsConfig.sidebarNav.map((item, index) => (
              <DocsSidebarNavGroup
                key={index}
                item={item}
                pathname={pathname}
                searchParams={searchParams}
                collapsedItems={collapsedItems}
                setCollapsedItems={setCollapsedItems}
              />
            ))}
          </div>
        </div>
      </SidebarContent>
      <SidebarFooter className="border-t px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            <span className="font-semibold">Instabee API</span> Documentation
          </div>
          <Button variant="ghost" size="icon" asChild>
            <a
              href="https://github.com/instabee"
              target="_blank"
              rel="noopener noreferrer"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-4 w-4"
              >
                <path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4" />
                <path d="M9 18c-4.51 2-5-2-7-2" />
              </svg>
              <span className="sr-only">GitHub</span>
            </a>
          </Button>
        </div>
      </SidebarFooter>
    </Sidebar>
    </SearchProvider>
  );
}

interface DocsSidebarNavGroupProps {
  item: NavItem;
  pathname: string;
  searchParams: URLSearchParams;
  collapsedItems: Set<string>;
  setCollapsedItems: React.Dispatch<React.SetStateAction<Set<string>>>;
  level?: number;
}

function DocsSidebarNavGroup({ item, pathname, searchParams, collapsedItems, setCollapsedItems, level = 0 }: DocsSidebarNavGroupProps) {
  // Check if this item is active based on pathname and query parameters
  const isActive = (() => {
    if (!item.href) return false;

    const [itemPath, itemQuery] = item.href.split('?');
    const currentSection = searchParams.get('section');

    // If the item has a query parameter, check both path and section
    if (itemQuery) {
      const itemParams = new URLSearchParams(itemQuery);
      const itemSection = itemParams.get('section');
      return pathname === itemPath && currentSection === itemSection;
    }

    // For items without query parameters, just check the path
    return pathname === itemPath && !currentSection;
  })();
  const isExactActive = item.href ? pathname === item.href : false;

  // Generate unique key for this item
  const itemKey = item.href || item.title;
  const isCollapsed = collapsedItems.has(itemKey);
  const hasChildren = item.children && item.children.length > 0;

  const toggleCollapse = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const newCollapsedItems = new Set(collapsedItems);
    if (isCollapsed) {
      newCollapsedItems.delete(itemKey);
    } else {
      newCollapsedItems.add(itemKey);
    }
    setCollapsedItems(newCollapsedItems);
  };

  const handleClick = (e: React.MouseEvent, href: string) => {
    // Check if this is a section link (has query parameter)
    if (href.includes('?section=')) {
      e.preventDefault();

      const [path, query] = href.split('?');
      const params = new URLSearchParams(query);
      const sectionId = params.get('section');

      // Only handle if we're on the same page
      if (pathname === path && sectionId) {
        // Update URL
        const url = new URL(window.location.href);
        url.searchParams.set('section', sectionId);
        window.history.pushState({}, '', url.toString());

        // Scroll to element
        const element = document.getElementById(sectionId);
        if (element) {
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest'
          });
        }
      } else {
        // Navigate to the page normally
        window.location.href = href;
      }
    }
  };

  return (
    <div>
      <div className="flex items-center">
        {item.href ? (
          <Link
            href={item.disabled || !item.href ? "#" : item.href}
            onClick={(e) => item.href && handleClick(e, item.href)}
            className={cn(
              "flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground flex-1",
              isActive ? "bg-accent text-accent-foreground" : "transparent",
              item.disabled && "cursor-not-allowed opacity-60",
              // Hierarchical styling based on level
              level === 0 && "text-sm font-medium",
              level === 1 && "ml-3 text-xs font-normal",
              level === 2 && "ml-6 text-xs font-normal text-muted-foreground",
              level === 3 && "ml-9 text-xs font-normal text-muted-foreground",
              level === 4 && "text-xs font-normal text-muted-foreground",
              level === 5 && "text-xs font-normal text-muted-foreground",
              level >= 6 && "text-xs font-normal text-muted-foreground"
            )}
            style={{
              marginLeft: level >= 4 ? `${(level - 1) * 12 + 12}px` : undefined
            }}
            )}
            target={item.external ? "_blank" : ""}
            rel={item.external ? "noreferrer" : ""}
          >
            {item.title}
            {item.label && (
              <span className="ml-2 rounded-md bg-[#adfa1d] px-1.5 py-0.5 text-xs text-[#000000]">
                {item.label}
              </span>
            )}
          </Link>
        ) : (
          <span className={cn(
            "flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium flex-1",
            // Hierarchical styling for non-link items
            level === 0 && "text-sm font-medium",
            level === 1 && "ml-3 text-xs font-semibold text-muted-foreground",
            level === 2 && "ml-6 text-xs font-semibold text-muted-foreground",
            level === 3 && "ml-9 text-xs font-semibold text-muted-foreground",
            level === 4 && "text-xs font-semibold text-muted-foreground",
            level === 5 && "text-xs font-semibold text-muted-foreground",
            level >= 6 && "text-xs font-semibold text-muted-foreground"
          )}
          style={{
            marginLeft: level >= 4 ? `${(level - 1) * 12 + 12}px` : undefined
          }}
          )}>
            {item.title}
          </span>
        )}

        {/* Collapse/Expand button */}
        {hasChildren && (
          <button
            onClick={toggleCollapse}
            className={cn(
              "p-1 rounded hover:bg-accent transition-colors",
              // Adjust margin based on level
              level === 0 && "mr-2",
              level === 1 && "mr-2",
              level === 2 && "mr-2",
              level === 3 && "mr-2",
              level === 4 && "mr-2",
              level >= 5 && "mr-2"
            )}
            aria-label={isCollapsed ? "Expand" : "Collapse"}
          >
            {isCollapsed ? (
              <ChevronRight className="h-3 w-3 text-muted-foreground" />
            ) : (
              <ChevronDown className="h-3 w-3 text-muted-foreground" />
            )}
          </button>
        )}
      </div>

      {/* Children */}
      {hasChildren && !isCollapsed && (
        <div className="mt-1">
          {item.children!.map((child, index) => (
            <DocsSidebarNavGroup
              key={index}
              item={child}
              pathname={pathname}
              searchParams={searchParams}
              collapsedItems={collapsedItems}
              setCollapsedItems={setCollapsedItems}
              level={level + 1}
            />
          ))}
        </div>
      )}
    </div>
  );
}
