"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { NavItem, docsConfig } from "@/config/docs-navigation";
import { Sidebar, <PERSON>barFooter, SidebarHeader, SidebarContent } from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { SearchButton } from "@/components/search/search-button";
import { SearchDialog } from "@/components/search/search-dialog";
import { SearchProvider } from "@/components/search/search-provider";

interface DocsSidebarProps {
  className?: string;
}

export function DocsSidebar({ className }: DocsSidebarProps) {
  const pathname = usePathname();

  return (
    <SearchProvider>
      <Sidebar className={cn("border-r bg-background", className)}>
        <SidebarHeader className="flex flex-col gap-3 h-auto py-4 border-b px-4">
          <Link href="/" className="flex items-center gap-2 font-semibold w-full">
            <span className="text-xl font-bold">Instabee API</span>
          </Link>
          <div className="w-full">
            <SearchButton />
            <SearchDialog />
          </div>
        </SidebarHeader>
      <SidebarContent>
        <div className="px-4 py-2">
          <h4 className="mb-2 text-sm font-semibold text-muted-foreground">Documentation</h4>
          <div className="space-y-1">
            {docsConfig.sidebarNav.map((item, index) => (
              <DocsSidebarNavGroup key={index} item={item} pathname={pathname} />
            ))}
          </div>
        </div>
      </SidebarContent>
      <SidebarFooter className="border-t px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            <span className="font-semibold">Instabee API</span> Documentation
          </div>
          <Button variant="ghost" size="icon" asChild>
            <a
              href="https://github.com/instabee"
              target="_blank"
              rel="noopener noreferrer"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-4 w-4"
              >
                <path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4" />
                <path d="M9 18c-4.51 2-5-2-7-2" />
              </svg>
              <span className="sr-only">GitHub</span>
            </a>
          </Button>
        </div>
      </SidebarFooter>
    </Sidebar>
    </SearchProvider>
  );
}

interface DocsSidebarNavGroupProps {
  item: NavItem;
  pathname: string;
  level?: number;
}

function DocsSidebarNavGroup({ item, pathname, level = 0 }: DocsSidebarNavGroupProps) {
  const isActive = item.href ? pathname === item.href || pathname?.startsWith(item.href) : false;
  const isExactActive = item.href ? pathname === item.href : false;

  return (
    <div>
      {item.href ? (
        <Link
          href={item.disabled || !item.href ? "#" : item.href}
          className={cn(
            "flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground",
            isActive ? "bg-accent text-accent-foreground" : "transparent",
            item.disabled && "cursor-not-allowed opacity-60",
            level > 0 && "ml-4 text-xs"
          )}
          target={item.external ? "_blank" : ""}
          rel={item.external ? "noreferrer" : ""}
        >
          {item.title}
          {item.label && (
            <span className="ml-2 rounded-md bg-[#adfa1d] px-1.5 py-0.5 text-xs text-[#000000]">
              {item.label}
            </span>
          )}
        </Link>
      ) : (
        <span className={cn(
          "flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium",
          level > 0 && "ml-4 text-xs font-semibold text-muted-foreground"
        )}>
          {item.title}
        </span>
      )}
      {item.children?.length ? (
        <div className={cn("mt-1", level > 0 && "ml-4")}>
          {item.children.map((child, index) => (
            <DocsSidebarNavGroup key={index} item={child} pathname={pathname} level={level + 1} />
          ))}
        </div>
      ) : null}
    </div>
  );
}
