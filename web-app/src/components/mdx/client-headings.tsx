'use client';

import { cn } from "@/lib/utils";
import { useEffect } from "react";
import { useSearchParams } from "next/navigation";

export function useScrollToSection(id: string | undefined) {
  const searchParams = useSearchParams();
  const activeSection = searchParams.get('section');
  const isActive = activeSection === id;
  
  useEffect(() => {
    if (isActive && id) {
      const element = document.getElementById(id);
      if (element) {
        setTimeout(() => {
          element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }, 100);
      }
    }
  }, [isActive, id]);
  
  return isActive;
}

export function ClientHeading({
  id,
  level,
  className,
  children,
}: {
  id?: string,
  level: 1 | 2 | 3 | 4 | 5 | 6,
  className?: string,
  children: React.ReactNode,
}) {
  const isActive = useScrollToSection(id);
  const Tag = level === 1 ? 'h1' : 
              level === 2 ? 'h2' : 
              level === 3 ? 'h3' : 
              level === 4 ? 'h4' : 
              level === 5 ? 'h5' : 'h6';

  function handleClick(e: React.MouseEvent) {
    e.preventDefault();
    if (id) {
      // Update URL with section parameter
      const url = new URL(window.location.href);
      url.searchParams.set('section', id);
      window.history.pushState({}, '', url.toString());
      
      // Scroll to the section
      const element = document.getElementById(id);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }
  }

  if (level === 1) {
    return (
      <h1 id={id} className={cn(isActive ? "scroll-m-20 border-l-4 border-amber-500 pl-2 -ml-3" : "scroll-m-20", className)}>
        {children}
        {id && (
          <a href={`#${id}`} onClick={handleClick} className="ml-2 opacity-0 hover:opacity-100 text-amber-500 text-sm group-hover:opacity-100" aria-label="Link to this section">#</a>
        )}
      </h1>
    );
  } else if (level === 2) {
    return (
      <h2 id={id} className={cn(isActive ? "scroll-m-20 border-l-4 border-amber-500 pl-2 -ml-3" : "scroll-m-20", className)}>
        {children}
        {id && (
          <a href={`#${id}`} onClick={handleClick} className="ml-2 opacity-0 hover:opacity-100 text-amber-500 text-sm group-hover:opacity-100" aria-label="Link to this section">#</a>
        )}
      </h2>
    );
  } else if (level === 3) {
    return (
      <h3 id={id} className={cn(isActive ? "scroll-m-20 border-l-4 border-amber-500 pl-2 -ml-3" : "scroll-m-20", className)}>
        {children}
        {id && (
          <a href={`#${id}`} onClick={handleClick} className="ml-2 opacity-0 hover:opacity-100 text-amber-500 text-sm group-hover:opacity-100" aria-label="Link to this section">#</a>
        )}
      </h3>
    );
  } else if (level === 4) {
    return (
      <h4 id={id} className={cn(isActive ? "scroll-m-20 border-l-4 border-amber-500 pl-2 -ml-3" : "scroll-m-20", className)}>
        {children}
        {id && (
          <a href={`#${id}`} onClick={handleClick} className="ml-2 opacity-0 hover:opacity-100 text-amber-500 text-sm group-hover:opacity-100" aria-label="Link to this section">#</a>
        )}
      </h4>
    );
  } else if (level === 5) {
    return (
      <h5 id={id} className={cn(isActive ? "scroll-m-20 border-l-4 border-amber-500 pl-2 -ml-3" : "scroll-m-20", className)}>
        {children}
        {id && (
          <a href={`#${id}`} onClick={handleClick} className="ml-2 opacity-0 hover:opacity-100 text-amber-500 text-sm group-hover:opacity-100" aria-label="Link to this section">#</a>
        )}
      </h5>
    );
  } else {
    return (
      <h6 id={id} className={cn(isActive ? "scroll-m-20 border-l-4 border-amber-500 pl-2 -ml-3" : "scroll-m-20", className)}>
        {children}
        {id && (
          <a href={`#${id}`} onClick={handleClick} className="ml-2 opacity-0 hover:opacity-100 text-amber-500 text-sm group-hover:opacity-100" aria-label="Link to this section">#</a>
        )}
      </h6>
    );
  }
}
