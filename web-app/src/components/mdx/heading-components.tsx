'use client';

import React, { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { cn } from "@/lib/utils";

export interface HeadingProps {
  id?: string;
  className?: string;
  children?: React.ReactNode;
  [key: string]: any;
}

export function H1({ id, className, children, ...props }: HeadingProps) {
  useScrollToSection(id);
  return renderHeading('h1', id, className, children, props);
}

export function H2({ id, className, children, ...props }: HeadingProps) {
  useScrollToSection(id);
  return renderHeading('h2', id, className, children, props);
}

export function H3({ id, className, children, ...props }: HeadingProps) {
  useScrollToSection(id);
  return renderHeading('h3', id, className, children, props);
}

export function H4({ id, className, children, ...props }: HeadingProps) {
  useScrollToSection(id);
  return renderHeading('h4', id, className, children, props);
}

export function H5({ id, className, children, ...props }: HeadingProps) {
  useScrollToSection(id);
  return renderHeading('h5', id, className, children, props);
}

export function H6({ id, className, children, ...props }: HeadingProps) {
  useScrollToSection(id);
  return renderHeading('h6', id, className, children, props);
}

function useScrollToSection(id?: string) {
  const searchParams = useSearchParams();
  const activeSection = searchParams.get('section');
  const isActive = activeSection === id;
  
  useEffect(() => {
    if (isActive && id) {
      const element = document.getElementById(id);
      if (element) {
        setTimeout(() => {
          element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }, 100);
      }
    }
  }, [isActive, id]);
  
  return isActive;
}

function renderHeading(tag: 'h1'|'h2'|'h3'|'h4'|'h5'|'h6', id?: string, className?: string, children?: React.ReactNode, props?: any) {
  const searchParams = useSearchParams();
  const activeSection = searchParams.get('section');
  const isActive = activeSection === id;
  
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    if (id) {
      // Update URL with section parameter
      const url = new URL(window.location.href);
      url.searchParams.set('section', id);
      window.history.pushState({}, '', url.toString());
      
      // Scroll to the section
      const element = document.getElementById(id);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }
  };
  
  const Component = tag;
  
  return (
    <Component 
      id={id} 
      className={cn(
        isActive ? "scroll-m-20 border-l-4 border-amber-500 pl-2 -ml-3" : "scroll-m-20",
        className
      )} 
      {...props}
    >
      {children}
      {id && (
        <a 
          href={`#${id}`} 
          onClick={handleClick}
          className="ml-2 opacity-0 hover:opacity-100 text-amber-500 text-sm group-hover:opacity-100"
          aria-label={`Link to ${String(children || '')}`}
        >
          #
        </a>
      )}
    </Component>
  );
}
