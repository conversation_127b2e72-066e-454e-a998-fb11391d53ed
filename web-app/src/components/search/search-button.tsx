"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { useSearch } from './search-provider';

export function SearchButton() {
  const { setIsOpen } = useSearch();

  return (
    <Button
      variant="outline"
      className="relative h-9 w-full justify-start text-sm text-muted-foreground pr-9 truncate"
      onClick={() => setIsOpen(true)}
    >
      <span className="truncate">Search docs...</span>
      <kbd className="pointer-events-none absolute right-1.5 top-1.5 h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 flex">
        <span className="text-xs">⌘</span>K
      </kbd>
    </Button>
  );
}
