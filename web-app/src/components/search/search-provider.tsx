"use client";

import React, { createContext, useContext, useState, ReactNode } from 'react';
import Fuse from 'fuse.js';
import { docsConfig } from '@/config/docs-navigation';

// Define the structure for search results
export interface SearchResult {
  title: string;
  path: string;
  content?: string;
  type: 'page' | 'section' | 'api' | 'parameter';
  highlight?: { 
    title?: string;
    content?: string;
  };
}

// Define the search context type
interface SearchContextType {
  query: string;
  setQuery: (query: string) => void;
  results: SearchResult[];
  searching: boolean;
  search: (query: string) => void;
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
}

// Create the context with a default value
const SearchContext = createContext<SearchContextType>({
  query: '',
  setQuery: () => {},
  results: [],
  searching: false,
  search: () => {},
  isOpen: false,
  setIsOpen: () => {},
});

// Create a custom hook to use the search context
export const useSearch = () => useContext(SearchContext);

// Generate a list of searchable items from the documentation
const generateSearchIndex = (): SearchResult[] => {
  const searchItems: SearchResult[] = [];

  // Add main navigation items
  docsConfig.sidebarNav.forEach(item => {
    if (item.href) {
      searchItems.push({
        title: item.title,
        path: item.href,
        type: 'page',
      });
    }

    // Add child items
    if (item.children) {
      item.children.forEach(child => {
        if (child.href) {
          searchItems.push({
            title: child.title,
            path: child.href,
            type: 'section',
          });
        }

        // Add grandchild items (parameters, etc.)
        if (child.children) {
          child.children.forEach(grandchild => {
            if (grandchild.href) {
              searchItems.push({
                title: grandchild.title,
                path: grandchild.href,
                type: 'parameter',
              });
            }
          });
        }
      });
    }
  });

  // Add API endpoints
  [
    { title: "Get Locker Per Postal Code", path: "/docs/availability#get-locker-per-postal-code", type: "api" },
    { title: "Get Locker Per Country", path: "/docs/availability#get-locker-per-country", type: "api" },
    { title: "Validate Postal Code", path: "/docs/availability#validate-postal-code", type: "api" },
    { title: "Get Delivery Slots", path: "/docs/availability#get-delivery-slots", type: "api" },
    { title: "Create Parcel", path: "/docs/post-purchase/create-parcel", type: "api" },
    { title: "Update Packed Parcel", path: "/docs/post-packing/create-parcel", type: "api" },
  ].forEach(item => {
    searchItems.push(item as SearchResult);
  });

  return searchItems;
};

// Create the search provider component
interface SearchProviderProps {
  children: ReactNode;
}

export function SearchProvider({ children }: SearchProviderProps) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [searching, setSearching] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  // Create a search index using Fuse.js
  const searchIndex = React.useMemo(() => {
    const items = generateSearchIndex();
    return new Fuse(items, {
      keys: ['title', 'content'],
      includeMatches: true,
      threshold: 0.3,
      distance: 100,
    });
  }, []);

  // Search function
  const search = (searchQuery: string) => {
    setQuery(searchQuery);
    
    if (!searchQuery.trim()) {
      setResults([]);
      return;
    }

    setSearching(true);
    
    try {
      const searchResults = searchIndex.search(searchQuery);
      
      // Transform Fuse.js results into our SearchResult format
      const formattedResults = searchResults.map(result => {
        const item = result.item;
        const matches = result.matches || [];
        
        // Create highlighted text if there are matches
        const highlight: { title?: string; content?: string } = {};
        
        matches.forEach(match => {
          if (match.key === 'title') {
            highlight.title = match.value;
          } else if (match.key === 'content') {
            highlight.content = match.value;
          }
        });
        
        return {
          ...item,
          highlight,
        };
      });
      
      setResults(formattedResults);
    } catch (error) {
      console.error('Search error:', error);
      setResults([]);
    } finally {
      setSearching(false);
    }
  };

  return (
    <SearchContext.Provider
      value={{
        query,
        setQuery,
        results,
        searching,
        search,
        isOpen,
        setIsOpen,
      }}
    >
      {children}
    </SearchContext.Provider>
  );
}
