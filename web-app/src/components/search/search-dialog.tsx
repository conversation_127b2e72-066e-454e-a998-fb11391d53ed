"use client";

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { useSearch } from './search-provider';

export function SearchDialog() {
  const router = useRouter();
  const { query, setQuery, results, searching, search, isOpen, setIsOpen } = useSearch();

  // Toggle the dialog when Cmd+K is pressed
  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === 'k' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        setIsOpen(!isOpen);
      }
    };

    document.addEventListener('keydown', down);
    return () => document.removeEventListener('keydown', down);
  }, [isOpen, setIsOpen]);

  // Handle search input changes
  const handleSearchInput = (value: string) => {
    setQuery(value);
    search(value);
  };

  // Handle item selection
  const handleSelect = (path: string) => {
    setIsOpen(false);
    router.push(path);
  };

  // Function to render highlighted content
  const renderHighlight = (text: string | undefined, fallback: string) => {
    if (!text) return fallback;
    return text;
  };

  // Get icon based on result type
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'page':
        return '📄';
      case 'section':
        return '📑';
      case 'api':
        return '🔌';
      case 'parameter':
        return '🔢';
      default:
        return '📎';
    }
  };

  return (
    <CommandDialog open={isOpen} onOpenChange={setIsOpen}>
      <CommandInput
        placeholder="Search documentation..."
        value={query}
        onValueChange={handleSearchInput}
      />
      <CommandList>
        <CommandEmpty>
          {searching ? 'Searching...' : 'No results found.'}
        </CommandEmpty>
        
        {results.length > 0 && !searching && (
          <CommandGroup heading="Results">
            {results.map((result, index) => (
              <CommandItem
                key={`${result.path}-${index}`}
                onSelect={() => handleSelect(result.path)}
                className="flex items-center"
              >
                <span className="mr-2">{getTypeIcon(result.type)}</span>
                <div>
                  <div className="font-medium">
                    {renderHighlight(result.highlight?.title, result.title)}
                  </div>
                  {result.highlight?.content && (
                    <div className="text-sm text-muted-foreground truncate max-w-md">
                      {result.highlight.content}
                    </div>
                  )}
                </div>
              </CommandItem>
            ))}
          </CommandGroup>
        )}
        
        <CommandGroup heading="Quick Links">
          <CommandItem onSelect={() => handleSelect('/docs/introduction')}>
            📚 Introduction
          </CommandItem>
          <CommandItem onSelect={() => handleSelect('/docs/authentication')}>
            🔒 Authentication
          </CommandItem>
          <CommandItem onSelect={() => handleSelect('/docs/availability')}>
            🔍 Availability API
          </CommandItem>
          <CommandItem onSelect={() => handleSelect('/docs/post-purchase')}>
            🛒 Post Purchase
          </CommandItem>
          <CommandItem onSelect={() => handleSelect('/docs/post-packing')}>
            📦 Post Packing
          </CommandItem>
        </CommandGroup>
      </CommandList>
    </CommandDialog>
  );
}
