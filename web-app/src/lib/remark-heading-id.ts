import { visit } from 'unist-util-visit';
import { toString } from 'mdast-util-to-string';

/**
 * Remark plugin to add IDs to headings
 */
export function remarkHeadingId() {
  return (tree: any) => {
    visit(tree, 'heading', (node) => {
      const text = toString(node);
      
      // Generate ID from text (similar to how GitHub does it)
      const id = text
        .toLowerCase()
        .replace(/[^\w\s-]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single
        .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
      
      // Add the ID as a data attribute that will be converted to an HTML id
      node.data = node.data || {};
      node.data.hProperties = node.data.hProperties || {};
      node.data.hProperties.id = id;
    });
  };
}
