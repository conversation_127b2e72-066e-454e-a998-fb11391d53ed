import fs from 'fs';
import path from 'path';

export async function readMarkdownFile(filePath: string): Promise<string> {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`Error reading markdown file: ${filePath}`, error);
    return '# Error loading content\n\nThe requested content could not be loaded.';
  }
}

export function extractHeadings(content: string): { id: string; title: string; level: number }[] {
  const headingRegex = /^(#{1,6})\s+(.+)$/gm;
  const headings = [];
  let match;

  while ((match = headingRegex.exec(content)) !== null) {
    const level = match[1].length;
    const title = match[2].trim();
    const id = title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-');

    headings.push({ id, title, level });
  }

  return headings;
}

export function generateTableOfContents(headings: { id: string; title: string; level: number }[]): string {
  return headings
    .map(heading => {
      const indent = '  '.repeat(heading.level - 1);
      return `${indent}- [${heading.title}](#${heading.id})`;
    })
    .join('\n');
}

export function insertTableOfContents(content: string, toc: string): string {
  const tocMarker = '<!-- TOC -->';
  if (content.includes(tocMarker)) {
    return content.replace(tocMarker, `${tocMarker}\n\n${toc}`);
  }
  
  // If no marker exists, insert after the first heading
  const firstHeadingRegex = /^#\s+.+$/m;
  const match = firstHeadingRegex.exec(content);
  
  if (match) {
    const index = match.index + match[0].length;
    return content.slice(0, index) + '\n\n' + toc + content.slice(index);
  }
  
  return content;
}

export function addIdToHeadings(content: string): string {
  return content.replace(
    /^(#{1,6})\s+(.+)$/gm,
    (match, hashes, title) => {
      const id = title
        .toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-');
      return `${hashes} ${title} {#${id}}`;
    }
  );
}

export function processMarkdown(content: string): string {
  const headings = extractHeadings(content);
  const toc = generateTableOfContents(headings);
  content = addIdToHeadings(content);
  
  // Include a table of contents for documents with multiple headings
  if (headings.length > 3) {
    content = insertTableOfContents(content, toc);
  }
  
  return content;
}
