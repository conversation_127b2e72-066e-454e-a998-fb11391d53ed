export interface Heading {
  id: string;
  text: string;
  level: number;
  children: Heading[];
}

/**
 * Extracts headings from markdown content and organizes them into a hierarchical structure
 * @param content Markdown content to parse
 * @returns Array of heading objects with nested children
 */
export function extractHeadingsFromMarkdown(content: string): Heading[] {
  // Find all headings (# Heading) in the content
  const headingRegex = /^(#{1,6})\s+(.+?)(?:\s*{\s*#([a-zA-Z0-9_-]+)\s*})?$/gm;
  const matches = Array.from(content.matchAll(headingRegex));
  
  const headings: Heading[] = [];
  let lastHeadingByLevel: Record<number, Heading> = {};
  
  matches.forEach((match) => {
    const level = match[1].length; // Number of # symbols
    const text = match[2].trim();
    // Use the explicit ID if provided, otherwise generate a slug from the text
    const id = match[3] || createSlug(text);
    
    const heading: Heading = {
      id,
      text,
      level,
      children: [],
    };
    
    // Determine where to add this heading in the hierarchy
    if (level === 1 || headings.length === 0) {
      // Top-level heading, add to the root array
      headings.push(heading);
    } else {
      // Find the closest parent heading
      let parentLevel = level - 1;
      while (parentLevel > 0) {
        if (lastHeadingByLevel[parentLevel]) {
          lastHeadingByLevel[parentLevel].children.push(heading);
          break;
        }
        parentLevel--;
      }
      
      // If no parent was found, add to the root array
      if (parentLevel === 0) {
        headings.push(heading);
      }
    }
    
    // Update the last heading for this level
    lastHeadingByLevel[level] = heading;
    
    // Clear all deeper levels when a new heading is encountered
    for (let i = level + 1; i <= 6; i++) {
      delete lastHeadingByLevel[i];
    }
  });
  
  return headings;
}

/**
 * Creates a URL-friendly slug from a string
 */
export function createSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/--+/g, '-') // Remove consecutive hyphens
    .trim(); // Trim leading/trailing whitespace
}

/**
 * Adds ID attributes to headings in markdown content for linking
 */
export function addIdsToHeadings(content: string): string {
  // Find headings without an ID and add one
  return content.replace(
    /^(#{1,6})\s+(.+?)(?:\s*{\s*#([a-zA-Z0-9_-]+)\s*})?$/gm, 
    (match, hashes, text, id) => {
      const slug = id || createSlug(text.trim());
      return `${hashes} ${text} {#${slug}}`;
    }
  );
}
