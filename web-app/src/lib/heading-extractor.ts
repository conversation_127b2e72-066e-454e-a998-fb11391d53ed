import { NavItem } from "@/config/docs-navigation";

export interface HeadingItem {
  id: string;
  text: string;
  level: number;
  href: string;
  children: HeadingItem[];
}

/**
 * Extracts headings from MDX content and builds a hierarchical structure
 */
export function extractHeadingsFromMDX(content: string, basePath: string): HeadingItem[] {
  const headingRegex = /^(#{1,6})\s+(.+)$/gm;
  const headings: HeadingItem[] = [];
  const stack: HeadingItem[] = [];
  
  let match;
  while ((match = headingRegex.exec(content)) !== null) {
    const level = match[1].length;
    const text = match[2].trim();
    
    // Generate ID from text (similar to how markdown processors do it)
    const id = text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
    
    const heading: HeadingItem = {
      id,
      text,
      level,
      href: `${basePath}?section=${id}`,
      children: []
    };
    
    // Find the correct parent for this heading
    while (stack.length > 0 && stack[stack.length - 1].level >= level) {
      stack.pop();
    }
    
    if (stack.length === 0) {
      // This is a top-level heading
      headings.push(heading);
    } else {
      // This is a child heading
      stack[stack.length - 1].children.push(heading);
    }
    
    stack.push(heading);
  }
  
  return headings;
}

/**
 * Converts HeadingItem[] to NavItem[] format for the sidebar
 */
export function headingsToNavItems(headings: HeadingItem[]): NavItem[] {
  return headings.map(heading => ({
    title: heading.text,
    href: heading.href,
    children: heading.children.length > 0 ? headingsToNavItems(heading.children) : undefined
  }));
}

/**
 * Flattens a hierarchical heading structure for easier searching
 */
export function flattenHeadings(headings: HeadingItem[]): HeadingItem[] {
  const flattened: HeadingItem[] = [];
  
  function traverse(items: HeadingItem[]) {
    for (const item of items) {
      flattened.push(item);
      if (item.children.length > 0) {
        traverse(item.children);
      }
    }
  }
  
  traverse(headings);
  return flattened;
}

/**
 * Pre-defined heading structures for each documentation page
 * This is a static approach since we can't dynamically read MDX at build time easily
 */
export const pageHeadings: Record<string, HeadingItem[]> = {
  '/docs/availability': [
    {
      id: 'overview',
      text: 'Overview',
      level: 2,
      href: '/docs/availability?section=overview',
      children: []
    },
    {
      id: 'key-concepts',
      text: 'Key Concepts',
      level: 2,
      href: '/docs/availability?section=key-concepts',
      children: [
        {
          id: 'availability-token',
          text: 'Availability Token',
          level: 3,
          href: '/docs/availability?section=availability-token',
          children: []
        },
        {
          id: 'postal-code-validation',
          text: 'Postal Code Validation',
          level: 3,
          href: '/docs/availability?section=postal-code-validation',
          children: []
        },
        {
          id: 'delivery-windows',
          text: 'Delivery Windows',
          level: 3,
          href: '/docs/availability?section=delivery-windows',
          children: []
        }
      ]
    },
    {
      id: 'endpoints',
      text: 'Endpoints',
      level: 2,
      href: '/docs/availability?section=endpoints',
      children: []
    },
    {
      id: 'request-parameters',
      text: 'Request Parameters',
      level: 2,
      href: '/docs/availability?section=request-parameters',
      children: [
        {
          id: 'query-parameters',
          text: 'Query Parameters',
          level: 3,
          href: '/docs/availability?section=query-parameters',
          children: []
        },
        {
          id: 'request-body',
          text: 'Request Body',
          level: 3,
          href: '/docs/availability?section=request-body',
          children: [
            {
              id: 'root-parameters',
              text: 'Root Parameters',
              level: 4,
              href: '/docs/availability?section=root-parameters',
              children: []
            },
            {
              id: 'sender',
              text: 'sender',
              level: 4,
              href: '/docs/availability?section=sender',
              children: [
                {
                  id: 'sender-coordinates',
                  text: 'sender.coordinates',
                  level: 5,
                  href: '/docs/availability?section=sender-coordinates',
                  children: []
                }
              ]
            },
            {
              id: 'recipient',
              text: 'recipient',
              level: 4,
              href: '/docs/availability?section=recipient',
              children: [
                {
                  id: 'recipient-coordinates',
                  text: 'recipient.coordinates',
                  level: 5,
                  href: '/docs/availability?section=recipient-coordinates',
                  children: []
                }
              ]
            },
            {
              id: 'dispatch',
              text: 'dispatch',
              level: 4,
              href: '/docs/availability?section=dispatch',
              children: []
            },
            {
              id: 'options',
              text: 'options',
              level: 4,
              href: '/docs/availability?section=options',
              children: [
                {
                  id: 'options-responsefields',
                  text: 'options.responseFields',
                  level: 5,
                  href: '/docs/availability?section=options-responsefields',
                  children: []
                }
              ]
            },
            {
              id: 'cart',
              text: 'cart',
              level: 4,
              href: '/docs/availability?section=cart',
              children: [
                {
                  id: 'cart-parcel',
                  text: 'cart.parcel',
                  level: 5,
                  href: '/docs/availability?section=cart-parcel',
                  children: []
                }
              ]
            }
          ]
        }
      ]
    },
    {
      id: 'response-parameters',
      text: 'Response Parameters',
      level: 2,
      href: '/docs/availability?section=response-parameters',
      children: [
        {
          id: 'common-response-fields',
          text: 'Common Response Fields',
          level: 3,
          href: '/docs/availability?section=common-response-fields',
          children: []
        },
        {
          id: 'locker-delivery-response',
          text: 'Locker Delivery Response',
          level: 3,
          href: '/docs/availability?section=locker-delivery-response',
          children: []
        },
        {
          id: 'home-delivery-response',
          text: 'Home Delivery Response',
          level: 3,
          href: '/docs/availability?section=home-delivery-response',
          children: []
        }
      ]
    },
    {
      id: 'error-handling',
      text: 'Error Handling',
      level: 2,
      href: '/docs/availability?section=error-handling',
      children: []
    },
    {
      id: 'example-usage---checking-home-delivery-availability',
      text: 'Example Usage - Checking Home Delivery Availability',
      level: 2,
      href: '/docs/availability?section=example-usage---checking-home-delivery-availability',
      children: []
    },
    {
      id: 'example-usage---finding-nearby-lockers',
      text: 'Example Usage - Finding Nearby Lockers',
      level: 2,
      href: '/docs/availability?section=example-usage---finding-nearby-lockers',
      children: []
    },
    {
      id: 'integration-best-practices',
      text: 'Integration Best Practices',
      level: 2,
      href: '/docs/availability?section=integration-best-practices',
      children: []
    }
  ]
};
