export interface NavItem {
  title: string;
  href?: string;
  disabled?: boolean;
  external?: boolean;
  label?: string;
  children?: NavItem[];
}

export interface DocsConfig {
  mainNav: NavItem[];
  sidebarNav: NavItem[];
}

export const docsConfig: DocsConfig = {
  mainNav: [
    {
      title: "Documentation",
      href: "/docs",
    },
    {
      title: "GitHub",
      href: "https://github.com/instabee",
      external: true,
    },
  ],
  sidebarNav: [
    {
      title: "Introduction",
      href: "/docs/introduction",
    },
    {
      title: "Authentication",
      href: "/docs/authentication",
    },
    {
      title: "Availability",
      href: "/docs/availability",
    },
    {
      title: "Post Purchase",
      href: "/docs/post-purchase",
      children: [
        {
          title: "Create Parcel API",
          href: "/docs/post-purchase/create-parcel",
        },
      ],
    },
    {
      title: "Post Packing",
      href: "/docs/post-packing",
      children: [
        {
          title: "Create Parcel API",
          href: "/docs/post-packing/create-parcel",
        },
      ],
    },
  ],
};
