export interface NavItem {
  title: string;
  href?: string;
  disabled?: boolean;
  external?: boolean;
  label?: string;
  children?: NavItem[];
  level?: number; // For heading hierarchy
}

export interface DocsConfig {
  mainNav: NavItem[];
  sidebarNav: NavItem[];
}

export const docsConfig: DocsConfig = {
  mainNav: [
    {
      title: "Documentation",
      href: "/docs",
    },
    {
      title: "GitHub",
      href: "https://github.com/instabee",
      external: true,
    },
  ],
  sidebarNav: [
    {
      title: "Introduction",
      href: "/docs/introduction",
    },
    {
      title: "Authentication",
      href: "/docs/authentication",
    },
    {
      title: "Availability",
      href: "/docs/availability",
      children: [
        {
          title: "Overview",
          href: "/docs/availability?section=overview",
          level: 2,
        },
        {
          title: "Key Concepts",
          href: "/docs/availability?section=key-concepts",
          level: 2,
          children: [
            {
              title: "Availability Token",
              href: "/docs/availability?section=availability-token",
              level: 3,
            },
            {
              title: "Postal Code Validation",
              href: "/docs/availability?section=postal-code-validation",
              level: 3,
            },
            {
              title: "Delivery Windows",
              href: "/docs/availability?section=delivery-windows",
              level: 3,
            },
          ],
        },
        {
          title: "Endpoints",
          href: "/docs/availability?section=endpoints",
          level: 2,
        },
        {
          title: "Request Parameters",
          href: "/docs/availability?section=request-parameters",
          level: 2,
          children: [
            {
              title: "Query Parameters",
              href: "/docs/availability?section=query-parameters",
              level: 3,
            },
            {
              title: "Request Body",
              href: "/docs/availability?section=request-body",
              level: 3,
              children: [
                {
                  title: "sender",
                  href: "/docs/availability?section=sender",
                  level: 4,
                  children: [
                    {
                      title: "sender.coordinates",
                      href: "/docs/availability?section=sender-coordinates",
                      level: 5,
                    },
                  ],
                },
                {
                  title: "recipient",
                  href: "/docs/availability?section=recipient",
                  level: 4,
                  children: [
                    {
                      title: "recipient.coordinates",
                      href: "/docs/availability?section=recipient-coordinates",
                      level: 5,
                    },
                  ],
                },
                {
                  title: "dispatch",
                  href: "/docs/availability?section=dispatch",
                  level: 4,
                },
                {
                  title: "options",
                  href: "/docs/availability?section=options",
                  level: 4,
                  children: [
                    {
                      title: "options.responseFields",
                      href: "/docs/availability?section=options-responsefields",
                      level: 5,
                    },
                  ],
                },
                {
                  title: "cart",
                  href: "/docs/availability?section=cart",
                  level: 4,
                  children: [
                    {
                      title: "cart.parcel",
                      href: "/docs/availability?section=cart-parcel",
                      level: 5,
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          title: "Response Parameters",
          href: "/docs/availability?section=response-parameters",
          level: 2,
          children: [
            {
              title: "Common Response Fields",
              href: "/docs/availability?section=common-response-fields",
              level: 3,
            },
            {
              title: "Locker Delivery Response",
              href: "/docs/availability?section=locker-delivery-response",
              level: 3,
            },
            {
              title: "Home Delivery Response",
              href: "/docs/availability?section=home-delivery-response",
              level: 3,
            },
          ],
        },
        {
          title: "Error Handling",
          href: "/docs/availability?section=error-handling",
          level: 2,
        },
        {
          title: "Integration Best Practices",
          href: "/docs/availability?section=integration-best-practices",
          level: 2,
        },
      ],
    },
    {
      title: "Post Purchase",
      href: "/docs/post-purchase",
      children: [
        {
          title: "Create Parcel API",
          href: "/docs/post-purchase/create-parcel",
        },
      ],
    },
    {
      title: "Post Packing",
      href: "/docs/post-packing",
      children: [
        {
          title: "Create Parcel API",
          href: "/docs/post-packing/create-parcel",
        },
      ],
    },
  ],
};
