export interface NavItem {
  title: string;
  href?: string;
  disabled?: boolean;
  external?: boolean;
  label?: string;
  children?: NavItem[];
  level?: number; // For heading hierarchy
  collapsed?: boolean; // For collapse/expand functionality
}

export interface DocsConfig {
  mainNav: NavItem[];
  sidebarNav: NavItem[];
}

export const docsConfig: DocsConfig = {
  mainNav: [
    {
      title: "Documentation",
      href: "/docs",
    },
    {
      title: "GitHub",
      href: "https://github.com/instabee",
      external: true,
    },
  ],
  sidebarNav: [
    {
      title: "Introduction",
      href: "/docs/introduction",
    },
    {
      title: "Authentication",
      href: "/docs/authentication",
      children: [
        {
          title: "API Keys",
          href: "/docs/authentication?section=api-keys",
          level: 2,
          children: [
            {
              title: "Obtaining an API Key",
              href: "/docs/authentication?section=obtaining-an-api-key",
              level: 3,
            },
          ],
        },
        {
          title: "Authentication Methods",
          href: "/docs/authentication?section=authentication-methods",
          level: 2,
        },
        {
          title: "Example Request",
          href: "/docs/authentication?section=example-request",
          level: 2,
        },
        {
          title: "API Key Security",
          href: "/docs/authentication?section=api-key-security",
          level: 2,
        },
        {
          title: "API Key Types",
          href: "/docs/authentication?section=api-key-types",
          level: 2,
        },
        {
          title: "Rate Limiting",
          href: "/docs/authentication?section=rate-limiting",
          level: 2,
        },
        {
          title: "Error Handling",
          href: "/docs/authentication?section=error-handling",
          level: 2,
        },
        {
          title: "Next Steps",
          href: "/docs/authentication?section=next-steps",
          level: 2,
        },
      ],
    },
    {
      title: "Availability",
      href: "/docs/availability",
      children: [
        {
          title: "Overview",
          href: "/docs/availability?section=overview",
          level: 2,
        },
        {
          title: "Key Concepts",
          href: "/docs/availability?section=key-concepts",
          level: 2,
          children: [
            {
              title: "Availability Token",
              href: "/docs/availability?section=availability-token",
              level: 3,
            },
            {
              title: "Postal Code Validation",
              href: "/docs/availability?section=postal-code-validation",
              level: 3,
            },
            {
              title: "Delivery Windows",
              href: "/docs/availability?section=delivery-windows",
              level: 3,
            },
          ],
        },
        {
          title: "Endpoints",
          href: "/docs/availability?section=endpoints",
          level: 2,
        },
        {
          title: "Request Parameters",
          href: "/docs/availability?section=request-parameters",
          level: 2,
          children: [
            {
              title: "Query Parameters",
              href: "/docs/availability?section=query-parameters",
              level: 3,
            },
            {
              title: "Request Body",
              href: "/docs/availability?section=request-body",
              level: 3,
              children: [
                {
                  title: "sender",
                  href: "/docs/availability?section=sender",
                  level: 4,
                  children: [
                    {
                      title: "sender.coordinates",
                      href: "/docs/availability?section=sender-coordinates",
                      level: 5,
                    },
                  ],
                },
                {
                  title: "recipient",
                  href: "/docs/availability?section=recipient",
                  level: 4,
                  children: [
                    {
                      title: "recipient.coordinates",
                      href: "/docs/availability?section=recipient-coordinates",
                      level: 5,
                    },
                  ],
                },
                {
                  title: "dispatch",
                  href: "/docs/availability?section=dispatch",
                  level: 4,
                },
                {
                  title: "options",
                  href: "/docs/availability?section=options",
                  level: 4,
                  children: [
                    {
                      title: "options.responseFields",
                      href: "/docs/availability?section=options-responsefields",
                      level: 5,
                    },
                  ],
                },
                {
                  title: "cart",
                  href: "/docs/availability?section=cart",
                  level: 4,
                  children: [
                    {
                      title: "cart.parcel",
                      href: "/docs/availability?section=cart-parcel",
                      level: 5,
                      children: [
                        {
                          title: "cart.parcel.products",
                          href: "/docs/availability?section=cart-parcel-products",
                          level: 6,
                        },
                        {
                          title: "cart.parcel.products.details",
                          href: "/docs/availability?section=cart-parcel-products-details",
                          level: 6,
                        },
                        {
                          title: "cart.parcel.products.details.price",
                          href: "/docs/availability?section=cart-parcel-products-details-price",
                          level: 6,
                        },
                        {
                          title: "cart.parcel.products.details.temperature",
                          href: "/docs/availability?section=cart-parcel-products-details-temperature",
                          level: 6,
                        },
                        {
                          title: "cart.parcel.products.packages",
                          href: "/docs/availability?section=cart-parcel-products-packages",
                          level: 6,
                        },
                        {
                          title: "cart.parcel.products.packages.barcodes",
                          href: "/docs/availability?section=cart-parcel-products-packages-barcodes",
                          level: 6,
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          title: "Response Parameters",
          href: "/docs/availability?section=response-parameters",
          level: 2,
          children: [
            {
              title: "Common Response Fields",
              href: "/docs/availability?section=common-response-fields",
              level: 3,
            },
            {
              title: "Locker Delivery Response",
              href: "/docs/availability?section=locker-delivery-response",
              level: 3,
            },
            {
              title: "Home Delivery Response",
              href: "/docs/availability?section=home-delivery-response",
              level: 3,
            },
          ],
        },
        {
          title: "Error Handling",
          href: "/docs/availability?section=error-handling",
          level: 2,
        },
        {
          title: "Integration Best Practices",
          href: "/docs/availability?section=integration-best-practices",
          level: 2,
        },
      ],
    },
    {
      title: "Post Purchase",
      href: "/docs/post-purchase",
      children: [
        {
          title: "Overview",
          href: "/docs/post-purchase?section=overview",
          level: 2,
          children: [
            {
              title: "API Endpoint",
              href: "/docs/post-purchase?section=api-endpoint",
              level: 3,
            },
            {
              title: "When to Use",
              href: "/docs/post-purchase?section=when-to-use",
              level: 3,
            },
            {
              title: "Integration Levels",
              href: "/docs/post-purchase?section=integration-levels",
              level: 3,
            },
          ],
        },
        {
          title: "Examples",
          href: "/docs/post-purchase?section=examples",
          level: 2,
          children: [
            {
              title: "Bare Minimum Integration",
              href: "/docs/post-purchase?section=bare-minimum-integration",
              level: 3,
            },
            {
              title: "Regular Integration",
              href: "/docs/post-purchase?section=regular-integration",
              level: 3,
            },
          ],
        },
        {
          title: "Key Benefits",
          href: "/docs/post-purchase?section=key-benefits",
          level: 2,
        },
        {
          title: "Next Steps",
          href: "/docs/post-purchase?section=next-steps",
          level: 2,
        },
        {
          title: "Quick Links",
          href: "/docs/post-purchase?section=quick-links",
          level: 2,
        },
        {
          title: "Create Parcel API",
          href: "/docs/post-purchase/create-parcel",
        },
      ],
    },
    {
      title: "Post Packing",
      href: "/docs/post-packing",
      children: [
        {
          title: "Overview",
          href: "/docs/post-packing?section=overview",
          level: 2,
          children: [
            {
              title: "API Endpoint",
              href: "/docs/post-packing?section=api-endpoint",
              level: 3,
            },
            {
              title: "When to Use",
              href: "/docs/post-packing?section=when-to-use",
              level: 3,
            },
            {
              title: "Integration Levels",
              href: "/docs/post-packing?section=integration-levels",
              level: 3,
            },
          ],
        },
        {
          title: "Examples",
          href: "/docs/post-packing?section=examples",
          level: 2,
          children: [
            {
              title: "Bare Minimum Integration",
              href: "/docs/post-packing?section=bare-minimum-integration",
              level: 3,
            },
            {
              title: "Minimum Integration",
              href: "/docs/post-packing?section=minimum-integration",
              level: 3,
            },
          ],
        },
        {
          title: "Key Benefits",
          href: "/docs/post-packing?section=key-benefits",
          level: 2,
        },
        {
          title: "Integration Workflow",
          href: "/docs/post-packing?section=integration-workflow",
          level: 2,
        },
        {
          title: "Next Steps",
          href: "/docs/post-packing?section=next-steps",
          level: 2,
        },
        {
          title: "Quick Links",
          href: "/docs/post-packing?section=quick-links",
          level: 2,
        },
        {
          title: "Create Parcel API",
          href: "/docs/post-packing/create-parcel",
        },
      ],
    },
  ],
};
