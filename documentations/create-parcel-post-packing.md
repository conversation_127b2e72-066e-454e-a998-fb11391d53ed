````markdown
# Post Packing flow

`/PUT api.instabee.com/orders`

## Locker:

### Bare Minimum

Everything is sent in the Post Purchase call.

```json
{
   "availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190", //or any of the other 6 integration paths
   "parcelPackingConfirmed": true
}
````

### Minimum

```json
{
  //"availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190",
  "parcelId": "PREFIX1234567890", // Can be left out if merchant wants us to generate it
  "brand": "instabox",
  "product": "LOCKER_EXPRESS",
  "recipient": {
    "name": "Test Testson",
    //"national_identification_number": 199004152012,
    "email": "<EMAIL>",
    "phone": "0701234567",
    "street": "Hälsingegatan 40",
    "street2": "Floor 10",
    "postalCode": "12345",
    "city": "Stockholm",
    "countryCode": "SE"
     //"coordinates": {
        //"long": 59.1111,
        //"lat": 15.1111
    },
  "deliveryOption": {
    "sort_code": "IN123"
  },
  "cart": { // or parcel? Some merchants might want to send multiple prebookings because they know that they will pack something in multiple parcels
    //"checkoutId": "XBO12-2923048",
    "orderNumber": "12345"
  }
}
```

### Maximum

```json
{
  "parcelPackingConfirmed": true, // should we have a confirmed/unconfirmed??
  "brand": "instabox",
  "product": "LOCKER_EXPRESS",
  "parcelGroupId": "123456789",
  "availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190",
  "parcelId": "PREFIX1234567890", // Can be left out if merchant wants us to generate it
  "brandId": "Brand1",
  "sender": {
    "name": "Merchant A",
    "email": "<EMAIL>",
    "phone": "0701234567",
    "street": "Hälsingegatan 40",
    "street2": "Floor 10",
    "postalCode": "12345",
    "city": "Stockholm",
    "countryCode": "SE",
      "coordinates": {
        "long": 59.1111,
        "lat": 15.1111
    }
  },
  "recipient": {
    "name": "Test Testson",
    "ssn": "199004152012",
    "email": "<EMAIL>",
    "phone": "0701234567",
    "street": "Hälsingegatan 40",
    "street2": "Floor 10",
    "postalCode": "12345",
    "city": "Stockholm",
    "countryCode": "SE",
     "coordinates": {
        "long": 59.1111,
        "lat": 15.1111
    }
  },
  "deliveryOption": {
    "sort_code": "IN123"
     //"etaInterval": {
       //"from": "2025-07-05T14:10:00.000Z"
       //"to": "2025-07-06T15:10:00.000Z" - Will default to the best available if the does not match
  },
  "dispatch": {
    "readyToShip": "2025-12-01T01:23:45.678Z", //readyToPack or OutOfStock or DoNotDeliverBefore
    "collectionPointId": "STOCKHOLM_12345",
    "PackStation": "Floor 1 Station 3",
    "returnPointId": "GBG_123"
  },
  "options": {
    "": "",
    "": "",
    "": "",
    "": "",
    "": "",
    "": "",
    "languageCode": "ENG"
  },
  "deliveryInstructions": {
    "notifyBy": "RING_DOORBELL", //KNOCK_ON_DOOR
    "doorCode": "1234",
    "message": "Hide it under the rock in the back",
    "intercom": true
  },
  "additionalServices": {
    "identification": {
      "type": "AGE_LIMIT", //AGE_LIMIT_AT_HANDOVER, SPECIFIC_PERSON, ANY_PERSON
      "ageLimit": 18,
      "ssn": 199004152012,
      "name": "John Doe"
      },
    "leaveByDoor": "ALLOW", // "DISALLOW" | "FORCE" (?)
    "leaveWithNeighbour": "ALLOW", // "DISALLOW" | "FORCE" (?) ,
    "numberOfMissRetries": 9 //minimum 1? null = defeault

  },
  "cart": {
    "checkoutId": "XBO12-2923048",
    "orderNumber": "12345",
    "totalValueInCents": 12300,
    "totalWeightGram": 30000,
    "parcel": {
        "heightCm": 10,
        "widthCm": 10,
        "lengthCm": 10,
        "volumeDm3": 1,
        "estimatedSize": "SMALL",
        "weightGram": 2000,
        "type": "BOX",
        "products": [
            {
                "name": "My Little Pony Deathmetal Limited Edition",
                "quantity": 1,
                "productId": "1234567",
                "details": {
                    "productType": "Prescription",
                    "imgUrl": "[https://tinyurl.com/2p9bu4kz](https://tinyurl.com/2p9bu4kz)",
                    "category": "Toy Horses",
                    "brand": "My Little Pony",
                    "description": "Let the best worlds of Death Metal and Ponies be combined with this awesome toy",
                    "price":{
                        "priceInCents": 14900,
                        "taxRateInCents": 2500,
                        "discountRateInCents": 10,
                        "currency": "SEK"
                    },
                    "temperature": {
                        "min": 8,
                        "max": 25
                    }
                },
                "packages": [
                    {
                        "widthCm": 5,
                        "heightCm": 5,
                        "lengthCm": 5,
                        "weightCm": 10,
                        "volumeDm3": 0.125,
                        "barcodes": [
                            {
                                "code": "***********",
                                "type": "EAN13"
                            }
                        ]
                    }
                ]
            }
        ]
    }
  }
}
```

```
```