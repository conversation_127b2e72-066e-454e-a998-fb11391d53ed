````markdown
# Availability Response Parameters

## Response Overview

### Locker Delivery
#### Get Lockers Per Postal Code

**Minimum:**

```json
{
    "showAsOptionInCheckout": true,
    "availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190",
    "checkoutId": "XBO12-2923048",
    "responseExpiresAt": "2025-06-27T12:00:00.000Z",
    "preselection": "RECOMMENDED",
    "deliveryOptions": [
        {
            "labelCode": "IN123",
            "sortCode": "IN123wOx9dJ",
            "type": "locker",
            "brand": "Instabox",
            "deliveryOption": "Central Station Stockholm",
            "deliveryOptionWithEta": "Central Station Stockholm (today ~16:10)",
            "eta": {
                "certainty": "EXACT", // INTERVAL or UNCERTAIN
                "etaExact": "2025-06-29T14:10:00.000Z",
                "etaInterval": { // Do not display the time component to users when interval
                    "from": "2025-07-05T14:10:00.000Z",
                    "to": "2025-07-06T15:10:00.000Z"
                }
            },
            "packBeforeDatetime": "2025-06-28T19:30:00.000Z",
            "consolidation": true
        }
    ]
}
````

**Maximum:**

```json
{
    "showAsOptionInCheckout": true,
    "availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190",
    "checkoutId": "XBO12-2923048",
    "responseExpiresAt": "2025-06-27T12:00:00.000Z",
    "preselection": "RECOMMENDED",
    "estimatedParcelType": "SIZE_A",
    "deliveryOptions": [
        {
            "labelCode": "IN123",
            "sortCode": "IN123wOx9dJ", // Aka sortCode 3.0
            "type": "locker",
            "brand": "Instabox",
            "deliveryOption": "Central Station Stockholm",
            "deliveryOptionWithEta": "Central Station Stockholm (today ~16:10)",
             "eta": {
                "certainty": "EXACT", // INTERVAL or UNCERTAIN
                "etaExact": "2025-06-29T14:10:00.000Z",
                "etaInterval": { // Do not display the time component to users when interval
                    "from": "2025-07-05T14:10:00.000Z",
                    "to": "2025-07-06T15:10:00.000Z"
                }
            },
            "localEta": {
                "date": "2025-06-29",
                "dayText": "today", // 2-3 days
                "etaTimeExact": "16:10" // empty if not eta.certainty === 'EXACT'
            },
            "consumerCutoffDatetime": "2025-06-27T12:00:00.000Z",
            "packBeforeDatetime": "2025-06-28T19:30:00.000Z",
            "directions": {
                "short": "Take the exit towards Vasagatan. Your Instabox is located in the Convenience Store at the exit",
                "long": "Take the exit towards Vasagatan. Your Instabox is located in the Convenience Store at the exit. Go to the man behind the counter with a moustache and answer these three questions: 1. What is your name? 2. What is your quest? 3. What is the air-speed velocity of an unladen swallow?"
            },
            "address": {
                "street": "Tulegatan 14",
                "postalCode": "11353",
                "city": "Stockholm",
                "countryCode": "SE",
                "country": "Sweden",
                "coordinates": {
                    "lat": 59.343971,
                    "long": 18.060796
                }
            },
            "deliveryPrice": {
                "priceInCents": 14900,
                "currency": "SEK"
            },
            "distance": {
                "unit": "meter",
                "value": 177,
                "text": "0.2 km",
                "type": "euclidean" // walking / driving
            },
            "openHours": [
                {
                    "date": "2025-06-29",
                    "isOpen": true,
                    "open": "2025-06-28T22:00:00.000Z",
                    "close": "2025-06-29T21:59:00.000Z",
                    "textLocal": "00:00-23:59"
                },
                {
                  "date": "2025-06-30",
                  "isOpen": true,
                  "open": "2025-06-29T22:00:00.000Z",
                  "close": "2025-06-30T21:59:00.000Z",
                  "textLocal": "00:00-23:59"
                },
                {
                  "date": "2025-07-01",
                  "isOpen": true,
                  "open": "2025-06-30T22:00:00.000Z",
                  "close": "2025-07-01T21:59:00.000Z",
                  "textLocal": "00:00-23:59"
                },
                {
                  "date": "2025-07-02",
                  "isOpen": true,
                  "open": "2025-07-01T22:00:00.000Z",
                  "close": "2025-07-02T21:59:00.000Z",
                  "textLocal": "00:00-23:59"
                },
                {
                  "date": "2025-07-03",
                  "isOpen": true,
                  "open": "2025-07-02T22:00:00.000Z",
                  "close": "2025-07-03T21:59:00.000Z",
                  "textLocal": "00:00-23:59"
                },
                {
                  "date": "2025-07-04",
                  "isOpen": true,
                  "open": "2025-07-03T22:00:00.000Z",
                  "close": "2025-07-04T21:59:00.000Z",
                  "textLocal": "00:00-23:59"
                },
                {
                  "date": "2025-07-05",
                  "isOpen": true,
                  "open": "2025-07-04T22:00:00.000Z",
                  "close": "2025-07-05T21:59:00.000Z",
                  "textLocal": "00:00-23:59"
                }
            ],
        "consolidation": true
        }
    ]
}
```

### Home Delivery

#### Get Delivery Slots

**Minimum:**

```json
{
    "showAsOptionInCheckout": true,
    "availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190",
    "checkoutId": "XBO12-2923048",
    "consumerCutoffDatetimeUtc": "2025-05-11T12:00:00.000Z", // checkoutCutoffDatetime/checkoutSessionExpiresAt
    "preselection": "RECOMMENDED", //do we need something similar for HD ever?
    "deliveryOptions": [
        {
            "sortCode": "INh0",
            "sortToken": "54acc100-cs2a",
            "type": "homedelivery",
            "brand": "Instabox",
            "deliveryOptionWithEta": "Måndag 13 maj, kl 16:00-22:30",
            //"deliveryOption": "Choose delivery time later",
              "eta": {
                "certainty": "EXACT", // INTERVAL or UNCERTAIN
                "etaExact": "2025-06-29T14:10:00.000Z",
                "etaInterval": {
                    "from": "2025-07-05T14:10:00.000Z", // Do not display the time component to users when interval
                    "to": "2025-07-06T15:10:00.000Z"
                }
            },
            "packBeforeDatetime": "2025-06-28T19:30:00.000Z",
            "consolidation": true
        }
    ]
}
```

**Maximum:**

```json
{
    "showAsOptionInCheckout": true,
    "availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190",
    "checkoutId": "XBO12-2923048",
    "responseExpiresAt": "2025-05-28T17:22:55.665Z",
    "preselection": "RECOMMENDED",
    "sender": {
        "name": "Merchant A",
        "email": "<EMAIL>",
        "phone": "0701234567",
        "street": "Hälsingegatan 40",
        "street2": "Floor 10",
        "city": "Stockholm",
            "coordinates": {
                "long": 59.1111,
                "lat": 15.1111
            }
    },
      "recipient": {
        "name": "Consumer B",
        "email": "<EMAIL>",
        "phone": "0701234567",
        "street": "Hälsingegatan 40",
        "street2": "Floor 10",
        "city": "Stockholm",
            "coordinates": {
                "long": 59.1111,
                "lat": 15.1111
            }
    },
    "deliveryOptions": [
        {
            "sortCode": "INh0",
            "sortToken": "54acc100-cs2a",
            "type": "homedelivery",
            "brand": "Instabox",
            "originCollectionPoint": "STOCKHOLM_12345",
            "deliveryOptionWithEta": "Måndag 13 maj, kl 16:00-22:30",
            //"deliveryOption": "Choose delivery time later",
          "eta": {
                "certainty": "EXACT", // INTERVAL or UNCERTAIN
                "etaExact": "2025-06-29T14:10:00.000Z",
                "etaInterval": { // Do not display the time component to users when interval
                    "from": "2025-07-05T14:10:00.000Z",
                    "to": "2025-07-06T15:10:00.000Z"
                }
            },
            "consumerCutoffDatetimeUtc": "2025-05-11T12:00:00.000Z",
            "packBeforeDatetimeUtc": "2025-05-12T19:30:00.000Z",
            "consolidation": true
        }
    ]
}
```

-----

## Response Body

| Param                  | Type         | Required | Description                                                                                                            | Constraints                     | Enum                             | Comment                                                                 |
|------------------------|--------------|----------|------------------------------------------------------------------------------------------------------------------------|---------------------------------|----------------------------------|-------------------------------------------------------------------------|
| **showAsOptionInCheckout** | boolean      | Required | Defines if Instabee option should be shown in the checkout or not. If “true” is returned, then show the option with the delivery options returned. If “false” is returned, do not show the option at all. | `true`/`false`                  |                                  |                                                                         |
| **availabilityToken** | string       | Required | This is a request & response ID. A key identifier for Instabee to know what we said in the checkout. Please send this on the Order Create if possible. Integration options exist, please ask your Instabee Tech Sales representative |                                 |                                  |                                                                         |
| **checkoutId** | string       | Optional | If a checkoutId was sent in the Availability API request then this is returned in this field. If nothing was sent then this field will be omitted from the response |                                 |                                  |                                                                         |
| **responseExpiresAt** | string       | Required | The time when the delivery options no longer will be valid.                                                            | ISO-8601-date                   |                                  |                                                                         |
| **preselection** | string       | Required | Instabee logic to define if we think the delivery options is good enough for our option to be preselected in the checkout. |                                 | `RECOMMENDED`, `ALLOW`, `DISALLOW` |                                                                         |
| **estimatedParcelType**| string       | Optional | The calculated size based on the parcel configuration and the product sizes sent in the Availability API request.        |                                 |                                  |                                                                         |
| **sender** | object       | Optional | The information sent in the sender object in the Availability API request                                              |                                 |                                  |                                                                         |
| **recipient** | object       | Optional | The information sent in the recipient object in the Availability API request                                           |                                 |                                  |                                                                         |
| **deliveryOptions** | array\<object\>| Required | Object will all of the delivery options that are valid for based on the information sent in the Availability API request |                                 |                                  |                                                                         |

### deliveryOptions.[ { } ]

| Param                    | Type         | Required  | Description                                                                                                                                                              | Constraints   | Enum                                   | Comment                                                      |
|--------------------------|--------------|-----------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------|----------------------------------------|--------------------------------------------------------------|
| **sortCode** | string       | Required  | Delivery Option ID (also known as Box ID, Locker ID, HD slot ID). This is the identifier used when creating an order with the option that the user has selected.           |               |                                        |                                                              |
| **labelCode** | string       | Required  | An alternative ID that should be added to the label only\! This ID is not allowed to be used when creating the order.                                                       |               |                                        |                                                              |
| **type** | string       | Required  | Delivery Option type. Will be e.g Locker or Home Delivery Slot                                                                                                           |               |                                        |                                                              |
| **brand** | string       | Required  | Brand of the delivery option                                                                                                                                             |               | `instabox`, `budbee`, `porterbuddy`    |                                                              |
| **collectionPoint** | string       | Optional  | The warehouse ID used in the Availability API request. Sent back as a confirmation.                                                                                      |               |                                        |                                                              |
| **deliveryOption** | string       | Required  | Name of the delivery option.                                                                                                                                             |               |                                        |                                                              |
| **deliveryOptionWithEta**| string       | Required\* | Name of the delivery option with a delivery time if this is activated. \*Will only be returned if ETA is set to be on for the credentials used.                           |               |                                        |                                                              |
| **eta** | object       | Required  | UTC ETA object with information about the delivery time                                                                                                                  | ISO-8601-date |                                        |                                                              |
| **localEta** | object       | Optional  | ETA object with localised version of the delivery time                                                                                                                   |               |                                        |                                                              |
| **consumerCutoffDatetime**| string       | Required  | The datetime where the ETA is no longer valid                                                                                                                            | ISO-8601-date |                                        |                                                              |
| **packBeforeDateTime** | string       | Required  | The datetime when the parcel needs to be ready for pickup. The final order create call needs to be sent before this deadline. This deadline will be dynamic depending on when Instabee needs the parcel to fulfill the consumer delivery promise. | ISO-8601-date |                                        |                                                              |
| **directions** | object       | Optional  | The directions to find the locker at the location                                                                                                                        |               |                                        |                                                              |
| **address** | object       | Optional  | The address of the locker                                                                                                                                                |               |                                        |                                                              |
| **deliveryPrice** | object       | Optional  | The price of the delivery option. This is a feature that needs agreement and configuration by Instabee.                                                                  |               |                                        | Currently only available for Porterbuddy                     |
| **distance** | object       | Optional  | The distance to the locker.                                                                                                                                              |               |                                        |                                                              |
| **openHours** | array\<object\>| Optional  | The open hours of the location where the locker is placed                                                                                                              |               |                                        |                                                              |
| **consolidation** | boolean      | Optional  | If the delivery option is a consolidation option or not                                                                                                                  |               | `true`/`false`                         |                                                              |

#### deliveryOptions.[ { } ].eta

| Param       | Type   | Required | Description                                                  | Constraints | Enum                  | Comment                              |
|-------------|--------|----------|--------------------------------------------------------------|-------------|-----------------------|--------------------------------------|
| **certainty**| string | Required | The ETA type that will define which param below will be returned |             | `exact`, `interval`, `uncertain` |                                      |
| **etaExact** | string | Optional | UTC format of the exact delivery time                        |             |                       |                                      |
| **etaInterval**| object | Optional | UTC format of the interval delivery time                     |             |                       |                                      |

##### deliveryOptions.[ { } ].eta.etaInterval

| Param | Type   | Required | Description                          | Constraints   | Enum | Comment |
|-------|--------|----------|--------------------------------------|---------------|------|---------|
| **from**| string | Optional | The earliest possible UTC delivery time| ISO-8601-date |      |         |
| **to** | string | Optional | The latest possible UTC delivery time  | ISO-8601-date |      |         |

#### deliveryOptions.[ { } ].localEta

| Param          | Type   | Required | Description                                   | Constraints | Enum | Comment |
|----------------|--------|----------|-----------------------------------------------|-------------|------|---------|
| **date** | string | Optional | Date returned in the format `YYYY-MM-DD`      |             |      |         |
| **dayText** | string | Optional | A specific string returned as “today”, “tomorrow”, “sunday” etc. |             |      |         |
| **etaTimeExact**| string | Optional | A time in the format `HH:MM`                  |             |      |         |

#### deliveryOptions.[ { } ].directions

| Param   | Type   | Required | Description                                          | Constraints | Enum | Comment |
|---------|--------|----------|------------------------------------------------------|-------------|------|---------|
| **short** | string | Required | A shorter version of the description of how to locate the delivery option |             |      |         |
| **long** | string | Required | A longer version of the description of how to locate the delivery option  |             |      |         |

#### deliveryOptions.[ { } ].address

| Param         | Type   | Required | Description                        | Constraints      | Enum | Comment |
|---------------|--------|----------|------------------------------------|------------------|------|---------|
| **street** | string | Required | The delivery option’s address      |                  |      |         |
| **postalCode**| string | Required | The delivery option’s postal code  |                  |      |         |
| **city** | string | Optional | The delivery option’s city         |                  |      |         |
| **countryCode**| string | Required | The delivery option’s country code | ISO 3166 alpha 2 |      |         |
| **country** | string | Required | The delivery option’s country      |                  |      |         |
| **coordinates**| object | Optional | The delivery options’s coordinates |                  |      |         |

##### deliveryOptions.[ { } ].address.coordinates

| Param | Type   | Required | Description                     | Constraints | Enum | Comment |
|-------|--------|----------|---------------------------------|-------------|------|---------|
| **lat** | number | Required | The delivery options’s latitude |             |      |         |
| **long**| number | Required | The delivery options’s longitude|             |      |         |

#### deliveryOptions.[ { } ].deliveryPrice

| Param           | Type   | Required | Description                                                  | Constraints                       | Enum             | Comment                           |
|-----------------|--------|----------|--------------------------------------------------------------|-----------------------------------|------------------|-----------------------------------|
| **priceInCents**| string | Required | The specific delivery options price. This must be agreed and configured by Instabee |                                   |                  | Currently only available for Porterbuddy |
| **currency** | string | Required | The currency of the priceInCents                             |                                   | `SEK`, `NOK`, `DKK`, `EUR` |                                   |

#### deliveryOptions.[ { } ].distance

| Param | Type   | Required | Description                                                | Constraints | Enum                   | Comment                        |
|-------|--------|----------|------------------------------------------------------------|-------------|------------------------|--------------------------------|
| **unit**| string | Optional | The unit of the distance e.g meter or km                 |             |                        |                                |
| **value**| number | Optional | The raw value of the distance                            |             |                        |                                |
| **text**| string | Optional | A cleanup text version of the distance e.g “0.2km”         |             |                        |                                |
| **type**| string | Optional | The type of distance returned. This will be handled dynamically |             | `euclidean`, `walking`, `driving` |                                |

#### deliveryOptions.[ { } ].openHours.[ { } ]

| Param     | Type    | Required | Description                                                          | Constraints   | Enum         | Comment |
|-----------|---------|----------|----------------------------------------------------------------------|---------------|--------------|---------|
| **date** | string  | Required | The date of the opening hours in the `YYYY-MM-DD` format               |               |              |         |
| **isOpen**| boolean | Required | If the location is open that specific day                              | `true`/`false`|              |         |
| **open** | string  | Optional | The UTC datetime when the location opens                             | ISO-8601-date |              |         |
| **close** | string  | Required | The UTC datetime when the location closes                            | ISO-8601-date |              |         |
| **textLocal**| string | Required | A localized version of the opening hours in the format `HH:MM - HH:MM` |               |              |         |

```
```