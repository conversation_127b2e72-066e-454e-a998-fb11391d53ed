````markdown
# Post Purchase flow

`/PUT api.instabee.com/orders`

## Locker:

### Bare Minimum

**Nothing**

### Minimum

This is the bare minimum we need to be able to at least reserve capacity for the delivery before the parcel is packed and ready.

```json
{
  "availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190", // OneOf this, or checkoutId, or orderNumber
  "brand": "instabox",
  "product": "LOCKER_EXPRESS",
  "cart": { // OneOf this, or orderNumber, or availailityToken
    "orderNumber": "12345" // OneOf this, or checkoutId, or availailityToken
  },
  "deliveryOption": {
    "sort_code": "IN123"
  }
}
````

### Regular

This is what we expect to get in the majority of cases even though we will aim to get more information

```json
{
  "availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190", // OneOf this, or checkoutId, or orderNumber
  "brand": "instabox",
  "product": "LOCKER_EXPRESS",
  "parcelId": "PREFIX1234567890", // Can be left out
  "recipient": {
    "name": "Test Testsson",
    "email": "<EMAIL>",
    "phone": "0701234567", //can be changed to optional with setting
    "street": "Hälsingegatan 40",
    "street2": "Floor 10",
    "city": "Stockholm",
    "countryCode": "SE"
  },
   "deliveryOption": {
    "sort_code": "IN123"
  },
   "cart": { // OneOf this, or orderNumber, or availailityToken
    "checkoutId": "XBO12-2923048",
    "orderNumber": "12345" // OneOf this, or checkoutId, or availailityToken
  }
}
```

### Maxi

```json
{
  "parcelPackingConfirmed": true,
  "brand": "instabox",
  "product": "LOCKER_EXPRESS",
  "parcelGroupId": "123456789",
  "availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190",
  "parcelId": "PREFIX1234567890", // Can be left out and generated by Instabee
  "brandId": "Brand1",
  "communicationName": "That Merchant Name",
  "sender": {
    "name": "Merchant A",
    "email": "<EMAIL>",
    "phone": "0701234567",
    "street": "Hälsingegatan 40",
    "street2": "Floor 10",
    "postalCode": "12345",
    "city": "Stockholm",
    "countryCode": "se"
      //"coordinates": {
        //"long": 59.1111,
        //"lat": 15.1111
    },
  "recipient": {
    "name": "Test Testson",
    "ssn": "199004152012",
    "email": "<EMAIL>",
    "phone": "0701234567",
    "street": "Hälsingegatan 40",
    "street2": "Floor 10",
    "postalCode": "12345",
    "city": "Stockholm",
    "countryCode": "SE"
     //"coordinates": {
        //"long": 59.1111,
        //"lat": 15.1111
  },
  "deliveryOption": {
    "sort_code": "IN123"
      //"etaInterval": {
        //"from": "2025-07-05T14:10:00.000Z"
        //"to": "2025-07-06T15:10:00.000Z" - Will default to the best available if the does not match
  },
  "dispatch": {
    "readyToShip": "2025-12-01T01:23:45.678Z", //readyToPack or OutOfStock or DoNotDeliverBefore
    "collectionPointId": "STOCKHOLM_12345",
    "returnPointId": "GBG_123"
  },
  "options": {
    "": "",
    "": "",
    "": "",
    "": "",
    "": "",
    "": "",
    "languageCode": "ENG"
  },
  "deliveryInstructions": {
    "notifyBy": "ring_doorbell", //knock_on_door
    "doorCode": "1234",
    "message": "Hide it under the rock in the back",
    "intercom": true
  },
  "additionalServices": {
    "identification": {
      "type": "age_limit", //age_limit_at_handover, specific_person, any_person
      "ageLimit": 18,
      "ssn": 199004152012,
      "name": "John Doe"
      },
    "leaveByDoor": "allow", // "disallow" | "force" (?)
    "leaveWithNeighbour": "allow", // "dissalow" | "force" (?) ,
    "numberOfMissRetries": 9 //minimum 1? null = defeault

  },
  "cart": {
    "checkoutId": "XBO12-2923048",
    "orderNumber": "12345",
    "totalValueInCents": 12300,
    "totalWeightGram": 30000,
    "parcel": {
        "heightCm": 10,
        "widthCm": 10,
        "lengthCm": 10,
        "volumeDm3": 1,
        "estimatedSize": "small",
        "weightGram": 2000,
        "type": "box",
        "products": [
            {
                "name": "My Little Pony Deathmetal Limited Edition",
                "quantity": 1,
                "productId": "1234567",
                "details": {
                    "productType": "Prescription",
                    "imgUrl": "[https://tinyurl.com/2p9bu4kz](https://tinyurl.com/2p9bu4kz)",
                    "category": "Toy Horses",
                    "brand": "My Little Pony",
                    "description": "Let the best worlds of Death Metal and Ponies be combined with this awesome toy",
                    "price":{
                        "priceInCents": 14900,
                        "taxRateInCents": 2500,
                        "discountRateInCents": 10,
                        "currency": "sek"
                    },
                    "temperature": {
                        "min": 8,
                        "max": 25
                    }
                },
                "packages": [
                    {
                        "widthMm": 5,
                        "heightMm": 5,
                        "lengthMm": 5,
                        "weightMm": 10,
                        "volumeMm3": 0.125,
                        "barcodes": [
                            {
                                "code": "***********",
                                "type": "ean13"
                            }
                        ]
                    }
                ]
            }
        ]
    }
  }
}
```
