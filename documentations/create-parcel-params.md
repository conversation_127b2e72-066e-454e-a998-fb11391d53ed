```markdown
# Create Order Request

## Endpoints Parameters

| Name          | Endpoint              | Type | Header                       | Comment                                   |
| :------------ | :-------------------- | :--- | :--------------------------- | :---------------------------------------- |
| Create Order  | `api.instabee.com/orders` | PUT  | Auth key, version, content header |                                           |

## Request Body

| Param                  | Type   | Required Post Packing | Description                                                                                                                                                                                                                             | Constraints                                                                                             | Enum                      | Comment                                                                                                                                   |
| :--------------------- | :----- | :-------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------ | :------------------------ | :---------------------------------------------------------------------------------------------------------------------------------------- |
| brand                  | string | optional              | Define brand of the delivery options                                                                                                                                                                                                    |                                                                                                         | `instabox` or `budbee`    |                                                                                                                                           |
| product                | string | required              | Define the product of the delivery options. Will be defined by tech sales in the integration process                                                                                                                                    | Must be one                                                                                             |                           |                                                                                                                                           |
| countryCode            | string | required* | Recipient country code. \*Can be sent in the body instead                                                                                                                                                                               | ISO 3166 alpha 2                                                                                        |                           |                                                                                                                                           |
| parcelPackConfirmed  | string | conditional* | Confirm if the parcel is packed and ready for pickup.                                                                                                                                                                                 | Not needed if: Two calls have been made. The second order create call will always confirm the parcel as ready. Can always be sent just in case |                           |                                                                                                                                           |
| parcelGroupId          | string | optional              | A unique ID that can group multiple parcels together. For e.g., Instabee consolidation product or if Instabee Group API is used                                                                                                             |                                                                                                         |                           |                                                                                                                                           |
| availabilityToken      | string | optional              | The same availability token returned from the Availability API response. Very important that it’s the same token based on the response that was shown                                                                                     |                                                                                                         |                           |                                                                                                                                           |
| orderId                | string | optional              | A unique order reference returned in the first call that can be used as the identifier for the next call                                                                                                                                |                                                                                                         |                           |                                                                                                                                           |
| parcelId               | string | optional              | The unique identifier for the barcode on the parcel. If omitted, Instabee will generate a unique ID.                                                                                                                                      |                                                                                                         |                           |                                                                                                                                           |
| brandId                | string | optional              | Can we used to define different brands on the same set of credentials                                                                                                                                                                   | Based on configuration, if no configuration is set then it will default to main brand on the account    | N/A                       |                                                                                                                                           |
| communicationName      | string | optional              | Define a special communication name. If omitted the default on the configuration will be used. This is mainly designed for merchants that want to communicate different names on the same integration                               |                                                                                                         |                           |                                                                                                                                           |
| sender                 | object | optional              | Mainly used for C2C sender information. Partner sender information is not currently used                                                                                                                                                | The idea is to use this for SME setups in the future                                                      |                           |                                                                                                                                           |
| recipient              | object | required              | Recipient information. \*Can be sent as a query param instead                                                                                                                                                                           |                                                                                                         |                           |                                                                                                                                           |
| dispatch               | object | optional              | Define when a parcel will be ready for pickup. And where it will be packed from, if partner uses multiple warehouses                                                                                                                   |                                                                                                         |                           |                                                                                                                                           |
| options                | object | optional              | Define what information should be returned in the response. Also possible to set language.                                                                                                                                              |                                                                                                         |                           |                                                                                                                                           |
| deliveryInstructions   | object | optional              |                                                                                                                                                                                                                                         |                                                                                                         |                           |                                                                                                                                           |
| additionalServices     | object | optional              |                                                                                                                                                                                                                                         |                                                                                                         |                           |                                                                                                                                           |
| cart                   | object | optional              | Send information about the parcel and/or products in the parcel                                                                                                                                                                         |                                                                                                         |                           |                                                                                                                                           |

---

## sender

| Param   | Type   | Required | Description                             | Constraints                        | Enum   | Comment                                            |
| :------ | :----- | :------- | :-------------------------------------- | :--------------------------------- | :----- | :------------------------------------------------- |
| name    | string | optional | Sender name                             |                                    |        |                                                    |
| email   | string | optional | Sender email address                    |                                    |        |                                                    |
| phone   | string | optional | Sender mobile phone number              | Min: 6 digits, Max 15 digits       |        |                                                    |
| street  | string | optional | Sender street name and number           | Min length >0                      |        |                                                    |
| street2 | string | optional | Additional sender street information    |                                    |        |                                                    |
| postalCode | string | optional\* | Sender postalCode. \*Is required for C2C product |                                    |        |                                                    |
| city    | string | optional | Sender city                             |                                    |        |                                                    |
| countryCode | string | optional\* | Sender country code. \*Is required for C2C product | ISO 3166 alpha 2                   |        |                                                    |
| coordinates | object | optional | Coordinates of the sender location (address) |                                    |        |                                                    |

---

## sender.coordinates

| Param | Type   | Required | Description                    | Constraints                                                                  | Enum   | Comment   |
| :---- | :----- | :------- | :----------------------------- | :--------------------------------------------------------------------------- | :----- | :-------- |
| long  | number | optional | Sender longitude geolocation   | We should specify supported constraint on number of decimal points for these |        |           |
| lat   | number | optional | Sender latitude geolocation    |                                                                              |        |           |

---

## recipient

| Param   | Type   | Required  | Description                      | Constraints       | Enum   | Comment   |
| :------ | :----- | :-------- | :------------------------------- | :---------------- | :----- | :-------- |
| name    | string | optional  | Recipient name                   |                   |        |           |
| ssn     | string | optional  |                                  |                   |        |           |
| email   | string | required\* | Recipient email                  |                   |        |           |
| phone   | string | required\* | Recipient mobile phone number    |                   |        |           |
| street  | string | required  | Recipient address                | Min length >0     |        |           |
| street2 | string | optional  | Recipient additional address information |                   |        |           |
| postalCode | string | required  | Recipient postal code            |                   |        |           |
| city    | string | required  | Recipient city                   |                   |        |           |
| countryCode | string | required  | Recipient country code           | ISO 3166 alpha 2  |        |           |
| coordinates | object | optional  | Recipient coordinates (address)  |                   |        |           |

---

## recipient.coordinates

| Param | Type   | Required | Description                     | Constraints | Enum   | Comment   |
| :---- | :----- | :------- | :------------------------------ | :---------- | :----- | :-------- |
| long  | number | optional | Recipient longitude geolocation |             |        |           |
| lat   | number | optional | Recipient latitude geolocation  |             |        |           |

---

## dispatch

| Param             | Type    | Required | Description                                                                                                                                | Constraints                                                                   | Enum        | Comment                                                        |
| :---------------- | :------ | :------- | :----------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------- | :---------- | :------------------------------------------------------------- |
| readyToShip       | string  | optional | When the parcel is ready for pickup. Instabee will look for the next pickup time based on this datetime                                  | ISO-8601-date Only one date of ready to ship/pack and out of stock can be used |             |                                                                |
| readyToPack       | string  | optional | When the parcels packing process will be initiated. Instabee will add packing time to this datetime                                      | ISO-8601-date Only one date of ready to ship/pack and out of stock can be used |             |                                                                |
| outOfStock        | boolean | optional | One of the products in the checkout is out of stock. This will remove the ETA from the delivery options. Parcel can be packed whenever in the future. | Only one date of ready to ship/pack and out of stock can be used              | true/false  |                                                                |
| doNotDeliverBefore| string  | optional | The date Instabee can’t deliver before. The ETA in the checkout will always be on or after the datetime sent                             | ISO-8601-date                                                                 |             |                                                                |
| packingTime       | number  | optional | The number of minutes of packing time that is required for the items in the checkout                                                       |                                                                               |             |                                                                |
| collectionPointId | string  | optional | This is to define a specific warehouse the products will be sent from. The warehouse ID will be defined by configuration on Instabee side |                                                                               |             |                                                                |
| returnPointId     | string  | optional | Define a specific return warehouse for the order                                                                                           |                                                                               |             |                                                                |

---

## options

| Param          | Type   | Required  | Description                                                                                                        | Constraints        | Enum   | Comment                                                                                         |
| :------------- | :----- | :-------- | :----------------------------------------------------------------------------------------------------------------- | :----------------- | :----- | :---------------------------------------------------------------------------------------------- |
| responseFields | object | optional  | Define which information should be returned in the Availability API response                                       |                    |        |                                                                                                 |
| languageCode   | string | optional  | Define the language of e.g descriptions and local term of the response. If omitted, this will default to the main language based on the countryCode | ISO 639-1          |        |                                                                                                 |

---

## options.responseFields

| Param             | Type    | Required | Description                                                                                                                                                                         | Constraints | Enum       | Comment                    |
| :---------------- | :------ | :------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------- | :--------- | :------------------------- |
| directions        | boolean | optional | If set to true the instructions how the user find the locker will be returned in the Availability API response                                                                      |             | true/false |                            |
| openHours         | boolean | optional | If set to true the open hours of the location where the locker is placed will be returned in the Availability API response                                                            |             | true/false |                            |
| distance          | boolean | optional | If set to true the distance from the user to the different delivery options will be returned in the Availability API response. Note that this distance is a precalculated estimated distance and depends on what recipient information is sent |             | true/false |                            |
| lockerAddress     | boolean | optional | If set to true the address to the locker location will be returned in the Availability API response                                                                                   |             | true/false | What if we launch PUDO?    |
| lockerCoordinates | boolean | optional | If set to true the longitude/latitude of the lockers location will be returned in the Availability API response                                                                     |             | true/false | What if we launch PUDO?    |
| price             | boolean | optional | If set to true a dynamic price for the delivery option can be returned in the Availability API response. This needs to be agreed and configured together with Instabee.                 |             | true/false |                            |
| localEtas         | boolean | optional | If set to true a set of different local terms of the delivery promise will be returned in the Availability API response                                                               |             | true/false |                            |
| estimatedParcelType | boolean | optional | If set to true a calculated estimated parcel size will be returned in the Availability API response. This needs to be configured                                                       |             | true/false |                            |
| sender            | boolean | optional | If set to true the sender information sent in the Availability API request will be returned                                                                                           |             | true/false |                            |
| recipient         | boolean | optional | If set to true the recipient information sent in the Availability API request will be returned                                                                                        |             | true/false |                            |

---

## deliveryInstructions

| Param      | Type    | Required | Description                                                                     | Constraints | Enum                         | Comment                                                   |
| :--------- | :------ | :------- | :------------------------------------------------------------------------------ | :---------- | :--------------------------- | :-------------------------------------------------------- |
| notifyBy   | string  | optional | How the courier should make their presence known at the time of delivery. This only applies when the delivery does not have leave outside door set |             | `ring_doorbell`, `knock_on_door` |                                                           |
| doorCode   | string  | optional | Door code to get into the building                                              |             |                              |                                                           |
| message    | string  | optional | Any specific message to the courier that can be helpful                         |             |                              |                                                           |
| intercom   | boolean | optional | If the entrance uses an intercom instead of door code                           |             | true/false                   |                                                           |

---

## additionalServices

| Param               | Type    | Required | Description                                                                                                           | Constraints      | Enum        | Comment                                                                   |
| :------------------ | :------ | :------- | :-------------------------------------------------------------------------------------------------------------------- | :--------------- | :---------- | :------------------------------------------------------------------------ |
| identification      | object  | optional | Define what type of identification type Instabee should perform before delivering the parcel to the customer          |                  |             |                                                                           |
| leaveOutsideDoor    | boolean | optional | Define if Instabee is allowed to place the parcel outside the door or not. Default is true                            |                  | true/false  |                                                                           |
| leaveWithNeighbour  | boolean | optional | Define if Instabee is allowed to deliver the parcel to a neighbour. Default is false                          |                  | true/false  |                                                                           |
| numberOfMissRetries | number  | optional | Define the number of retries Instabee is allowed to perform when failing the delivery. This will come with extra cost for the delivery | Min: 1, Max 5    |             |                                                                           |

---

## additionalServices.identification

| Param  | Type   | Required  | Description                                                                                                     | Constraints | Enum                                                      | Comment                                                                      |
| :----- | :----- | :-------- | :-------------------------------------------------------------------------------------------------------------- | :---------- | :-------------------------------------------------------- | :--------------------------------------------------------------------------- |
| type   | object | optional  | Type of identification method                                                                                   |             | `age_limit`, `age_limit_at_handover`, `specific_person`, `any_person` |                                                                              |
| ageLimit | number | optional  | The minimum age that is allowed for Instabee to fulfill the delivery                                            |             |                                                           |                                                                              |
| ssn    | string | optional\* | The social security number of the recipient. Required if type specific_person is used. “ssn” can also be sent under recipient information |             |                                                           |                                                                              |
| name   | string | optional  | The name of the person Instabee should identify. Can be used for home delivery and type specific_person. Will default to SSN in cases it’s possible |             |                                                           |                                                                              |

---

## cart

| Param             | Type   | Required | Description                                         | Constraints | Enum   | Comment   |
| :---------------- | :----- | :------- | :-------------------------------------------------- | :---------- | :----- | :-------- |
| checkoutId        | string | optional | The checkout session ID                             |             |        |           |
| orderNumber       | string | optional | The merchants order number                          |             |        |           |
| totalValueInCents | number | optional | Total value of the contents inside of the cart/parcel |             |        |           |
| parcel            | object | optional | Parcel object                                       |             |        |           |

---

## cart.parcel

| Param          | Type   | Required | Description                                                                                                                                  | Constraints | Enum                           | Comment                                                              |
| :------------- | :----- | :------- | :------------------------------------------------------------------------------------------------------------------------------------------- | :---------- | :----------------------------- | :------------------------------------------------------------------- |
| heightMm       | number | optional | Height of the parcel in mm                                                                                                                   |             |                                |                                                                      |
| widthMm        | number | optional | Width of the parcel in mm                                                                                                                    |             |                                |                                                                      |
| lengthMm       | number | optional | Length of the parcel in mm                                                                                                                   |             |                                |                                                                      |
| volumeDm3      | number | optional | Volume of the parcel in liters                                                                                                               |             |                                |                                                                      |
| estimatedSize  | string | optional | Estimated size of the parcel. The sizes will be configured together with Instabee                                                          |             | `SMALL`, `LARGE`, `TOOLARGE`   |                                                                      |
| weightGram     | number | optional | The weight of the parcel in gram                                                                                                             |             |                                |                                                                      |
| type           | string | optional | Type of the parcel material                                                                                                                  |             | `BOX`, `BAG`                   |                                                                      |
| products       | object | optional | Products inside of the parcel                                                                                                                |             |                                |                                                                      |

---

## cart.parcel.products

| Param     | Type   | Required | Description                                       | Constraints | Enum   | Comment   |
| :-------- | :----- | :------- | :------------------------------------------------ | :---------- | :----- | :-------- |
| name      | string | optional | Name of the product                               |             |        |           |
| quantity  | string | optional | Quantity of the product                           |             |        |           |
| productId | string | optional | The unique identifier of the product e.g EAN number |             |        |           |
| details   | string | optional | Details of the product                            |             |        |           |
| temperature | string | optional | Temperature restraints of the product               |             |        |           |

---

## cart.parcel.products.details

| Param       | Type   | Required | Description                                       | Constraints | Enum   | Comment                              |
| :---------- | :----- | :------- | :------------------------------------------------ | :---------- | :----- | :----------------------------------- |
| productType | string | optional | Type of product, e.g Toys                         |             |        |                                      |
| imgUrl      | string | optional | An URL link to the image of the product           |             |        |                                      |
| category    | string | optional | The category of the product e.g Plushie           |             |        |                                      |
| brand       | string | optional | The brand of the product                          |             |        |                                      |
| description | string | optional | The description of the product                    |             |        |                                      |
| price       | object | optional | The price of the product for a single item        |             |        |                                      |

---

## cart.parcel.products.details.price

| Param              | Type   | Required | Description                                        | Constraints   | Enum   | Comment   |
| :----------------- | :----- | :------- | :------------------------------------------------- | :------------ | :----- | :-------- |
| priceInCents       | number | optional | Price of a single item in cents without discount   |               |        |           |
| taxRateInCents     | number | optional | The tax rate of the item in cents                  | %             |        |           |
| discountRateInCents| number | optional | The active discount set on the product in cents    |               |        |           |
| currency           | string | optional | The currency of the checkout session               |               |        |           |

---

## cart.parcel.products.details.temperature

| Param | Type   | Required | Description                                                                                                       | Constraints          | Enum   | Comment   |
| :---- | :----- | :------- | :---------------------------------------------------------------------------------------------------------------- | :------------------- | :----- | :-------- |
| min   | number | optional | The minimum temperature the product can experience during transport and at the delivery option before pickup. Temperature in celsius | Min 8- Max 25        |        |           |
| max   | number | optional | The maximum temperature the product can experience during transport and at the delivery option before pickup. Temperature in celsius |                      |        |           |

---

## cart.parcel.products.packages

| Param     | Type   | Required | Description                                   | Constraints | Enum   | Comment   |
| :-------- | :----- | :------- | :-------------------------------------------- | :---------- | :----- | :-------- |
| heightMm  | string | optional | The height of the individual product package in mm |             |        |           |
| widthMm   | string | optional | The width of the individual product package in mm  |             |        |           |
| lengthMm  | string | optional | The length of the individual product package in mm |             |        |           |
| weightGram| string | optional | The weight of the individual product package in gram |             |        |           |
| volumeDm3 | string | optional | The volume of the individual product package in liters |             |        |           |
| barcodes  | object | optional | The barcodes of the individual product        |             |        |           |

---

## cart.parcel.products.packages.barcodes

| Param | Type   | Required | Description                      | Constraints | Enum   | Comment   |
| :---- | :----- | :------- | :------------------------------- | :---------- | :----- | :-------- |
| code  | string | optional | The content of the barcode e.g 123-456-789 |             |        |           |
| type  | string | optional | The barcode type e.g EAN13       |             |        |           |
```