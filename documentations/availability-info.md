````markdown
**Availability**

**Introduction:**

Instabee Availability API will utilise the same structure for both lockers and home delivery but there will of course be some differences. The new API will be a “layer on top” of the current stack.

Current setup looks something like this:

With Instabee API we will be able to clean up the above mess

And in the future this will enable us to get to this beautiful stage.

**Lockers:** **Get lockers via postalCode:** **Request:** **Minimal**

Endpoint:

**/POST** api.instabee.com/availability/postal-codes/12345/lockers?brand=instabox\&product=box1\&country=SE

Simple call with:

-   **postalCode:** The consumers postal code
-   **Brand:** Instabox or Budbee
-   **Product:** The specific product e.g., Express, Collect In Store, Green, Standard, Extra Fast
-   **Country Code**: The country that the postal code is located in

------------------------------------------------------------------------------------------

**Optimal**

Endpoint:

**/POST** api.instabee.com/availability/postal-codes/12345/lockers?brand=instabox\&product=box1\&country=SE

Body:

```json
{
  "recipient": {
    "email": "<EMAIL>",
    "street": "Hälsingegatan 40",
    "street2": "Floor 10",
    "city": "Stockholm"
  }
}
````

Explanation:

  - **postalCode:** The consumers postal code
  - **Brand:** Instabox or Budbee lockers
  - **Product:** The specific product e.g Express, Collect In Store, Green, Standard, Extra Fast
  - **Country Code**: The country that the postal code is located in
  - **Recipient** information in body (query param is possible also)
      - **Email**: With this we can geolocate the user better if it’s a returning customer. We can also return favorite lockers
      - **Street/city:** With this we can geolocate the user better

-----

**Maximum**

Endpoint:

**/POST** api.instabee.com/availability/postal-codes/12345/lockers?brand=instabox\&product=box1\&country=SE

Body:

```json
{
  "brandId": "Brand1",
  "sender": {
    "name": "Merchant A",
    "email": "<EMAIL>",
    "phone": "0701234567",
    "street": "Hälsingegatan 40",
    "street2": "Floor 10",
    "city": "Stockholm",
    "coordinates": {
        "long": 59.1111,
        "lat": 15.1111
    }
  },
  "recipient": {
    "email": "<EMAIL>",
    "phone": "0701234567",
    "street": "Hälsingegatan 40",
    "street2": "Floor 10",
    "city": "Stockholm",
    "coordinates": {
        "long": 59.1111,
        "lat": 15.1111
    }
  },
  "dispatch": {
    "readyToShip": "2025-12-01T01:23:45.678Z", //readyToPack or OutOfStock or DoNotDeliverBefore
    //"packingTime": 2,
     //"pickupWindows": \[ \<--Thinking to use this for SME in the future. They can send their opening hours and we can dynamically decide when to PU.
            //{"start": "2025-02-13T10:00+01:00", "end": "2025-02-13T18:00+01:00"},
            //{"start": "2025-02-14T10:00+01:00", "end": "2025-02-14T18:00+01:00"},
    "collectionPointId": "STOCKHOLM\_12345"
  },
  "options": {
    "responseFields": {
      "directions": true,
      "openhours": true,
      "distance": true,
      "lockerAddress": true,
      "coordinates": true,
      "price":true,
      "localEtas": true,
      "estimatedParcelType": true
    },
    "languageCode": "en\_US"
  },
  "cart": {
    "checkoutId": "XBO12-2923048",
    "orderNumber": "12345",
    "totalValueInCents": 12300,
    "totalWeightGram": 30000,
    "parcel": {
        "heightCm": 10,
        "widthCm": 10,
        "lengthCm": 10,
        "volumeDm3": 1,
        "estimatedSize": "SMALL",
        "weightGram": 2000,
        "type": "BOX",
        "products": \[
            {
                "name": "My Little Pony Deathmetal Limited Edition",
                "quantity": 1,
                "productId": "1234567",
                "details": {
                    "productType": "Prescription",
                    "imgUrl": "[https://tinyurl.com/2p9bu4kz](https://tinyurl.com/2p9bu4kz)",
                    "category": "Toy Horses",
                    "brand": "My Little Pony",
                    "description": "Let the best worlds of Death Metal and Ponies be combined with this awesome toy",
                    "price":{
                        "priceInCents": 14900,
                        "taxRateInCents": 2500,
                        "discountRateInCents": 10,
                        "currency": "SEK"
                    },
                    "temperature": {
                        "min": 8,
                        "max": 25
                    }
                },
                "packages": \[
                    {
                        "widthCm": 5,
                        "heightCm": 5,
                        "lengthCm": 5,
                        "weightCm": 10,
                        "volumeDm3": 0.125,
                        "barcodes": \[
                            {
                                "code": "***********",
                                "type": "EAN13"
                            }
                        \]
                    }
                \]
            }
        \]
    }
  }
}
```

Explanation:

  - **postalCode:** The consumers postal code
  - **Brand:** Instabox or Budbee lockers
  - **Product:** The specific product e.g Express, Collect In Store, Green, Standard, Extra Fast
  - **Country Code**: The country that the postal code is located in
  - **Sender** information: C2C sender information or e.g SME sender information
  - **Recipient** information in body (query param is possible also)
      - **Email**: With this we can geolocate the user better if it’s a returning customer. We can also return favorite lockers
      - **Street:** With this we can geolocate the user better
  - **Dispatch**: Information about where a parcel will be shipped from or when it will be packed and ready for pickup
  - **Options**: Define language or API response options
  - **Cart:** Send information about the value, weight and size information about the products. This can be used for calculating the size of the parcel or used for e.g identification options

-----

**Response:** **Minimum (default)**

This is to minimize the payload size

```json
{
    "showAsOptionInCheckout": true,
    "availabilityToken": "b4a9c200-c02a-4186-b3e8-6271a0be2190",
    "checkoutId": "XBO12-2923048",
    "consumerCutoffDatetime": "2025-06-27T12:00:00.000Z", // checkoutCutoffDatetime/checkoutSessionExpiresAt
    "preselection": "RECOMMENDED",
    "deliveryOptions": \[
      {
        "sortCode": "IN123",
            "type": "locker",
            "brand": "Instabox",
            "name": "Central Station Stockholm",
            "deliveryOptionWithEta": "Central Station Stockholm (today \~16:10)",
            "
```